# AI Live Streamer - 简化版并发架构设计文档 (v2.0)

## 1. 核心理念与原则

### 1.1. 背景：从过度设计中回归

经过多次迭代和问题排查，我们深刻认识到，先前试图采用复杂状态机（如LangGraph）来管理实时音频流的架构，是一个根本性的**“范式不匹配” (Paradigm Mismatch)**。该模式导致了状态同步困难、逻辑空转、响应性差以及不必要的复杂性等一系列问题。

### 1.2. 新核心原则：大道至简 (KISS)

本设计文档标志着一次彻底的架构回归。我们抛弃不合适的复杂工具，回归到问题的本质：一个**简单的并发流处理任务**。

新架构遵循以下核心原则：

*   **单一职责 (Single Responsibility Principle)**: 每个组件只做一件事并把它做好。`Player` 只管播放，`Controller` 只管协调。
*   **简单并发 (Simple Concurrency)**: 使用 Python `asyncio` 的原生并发原语（`Task`, `Queue`, `Event`）来管理并行的长时间运行任务，而不是依赖外部状态机。
*   **明确的控制流 (Explicit Control Flow)**: 控制逻辑是清晰、直接的 Python 代码，易于理解、调试和维护。
*   **无共享状态（或最小化共享）(No Shared State)**: 组件之间通过显式的方法调用和消息传递进行通信，避免复杂的、需要同步的共享状态对象。

## 2. 新架构图

新架构是一个经典的控制器-执行者模型，通过轻量级的异步原语进行协调。

```mermaid
graph TD
    subgraph API层
        A[FastAPI Endpoints]
    end

    subgraph 控制层 - Controller
        B(LiveStreamController<br/>纯协调器)
        C{{asyncio.Queue<br/>question_queue}}
        D{{asyncio.Event<br/>pause_event}}
    end

    subgraph 执行层 (Actors/Workers)
        E(MainContentPlayer)
        F(QAManager)
    end

    subgraph 外部服务
        G[TTS Service]
        H[LLM Service]
    end

    A -- "1. 创建/启动" --> B;
    A -- "4. 提交问题" --> C;

    B ==> |"2. create_task()"| E;
    B ==> |"2. create_task()"| F_Monitor["monitor_questions()<br/>async Task"];
    
    F_Monitor -.-> |"5. 发现问题<br/>await"| B;
    
    B --> |"6. pause()"| D;
    B ==> |"7. await handle_qa()"| F;
    B --> |"9. resume()"| D;

    E -- "3. play()" --> G;
    E -- "依赖" --> D;

    F -- "8. invoke()" --> H;
    F -- "8. play_qa()" --> G;
    
```

## 3. 组件职责定义

### 3.1. `API Layer` (e.g., `api/control.py`)
*   **职责**: 作为系统的HTTP入口。
*   **功能**:
    1.  提供 `/start-streaming` 接口，接收脚本内容，创建并启动 `LiveStreamController` 的一个新实例。
    2.  提供 `/submit-question` 接口，接收用户问题，并将其放入 `LiveStreamController` 实例的 `question_queue` 中。
    3.  不包含任何复杂的直播逻辑。

### 3.2. `LiveStreamController` (核心协调器)
*   **职责**: 直播生命周期的中心协调者。**纯粹的任务协调器**，不持有资源。
*   **功能**:
    1.  **生命周期管理**: 在 `start()` 方法中，使用 `asyncio.create_task` 创建并管理两个核心后台任务：
        *   `play_task`: 用于执行 `MainContentPlayer.play()`。
        *   `qa_monitor_task`: 用于执行 `self.monitor_questions()`。
    2.  **中断协调**:
        *   `monitor_questions` 循环会 `await` `session.question_queue.get()`。
        *   当收到问题时，它会调用 `self._interrupt_and_handle_qa()`。
        *   此方法会：
            a. 调用 `MainContentPlayer.pause()`。
            b. `await` `QAManager.handle_qa(question)`。
            c. 调用 `MainContentPlayer.resume()`。
    3.  **依赖注入**: 通过构造函数接收所有依赖：
        *   `player`: `IMainContentPlayer` 接口的实现（通过DI注入）。
        *   `qa_manager`: `IQAManager` 接口的实现（通过DI注入）。
        *   `session`: `LiveStreamSession` 实例，持有会话资源。
    4.  **任务引用**: 仅持有对创建的后台任务的引用，用于生命周期管理：
        *   `self.play_task`, `self.qa_monitor_task`: 任务引用，用于优雅地取消和清理。

### 3.3. `MainContentPlayer` (主内容播放器 - Actor)
*   **角色定位**: 作为独立的 **Actor/Worker**，长时间运行的执行单元。
*   **职责**: **只负责**接收并播放主线脚本内容。
*   **功能**:
    1.  `play(script: List[str])`: 一个长时间运行的异步方法。它会循环遍历脚本句子，调用TTS服务，并将音频流式传输。
    2.  `pause()`: 设置一个内部的 `asyncio.Event` (`self._pause_event.clear()`)，使得 `play` 循环在下一句开始前暂停。
    3.  `resume()`: 清除该事件 (`self._pause_event.set()`)，让 `play` 循环继续。
    4.  `stop()`: 优雅地取消内部播放任务并清理TTS连接。
    5.  **内部状态**: 维护自身的播放状态（如当前句子索引）和TTS引擎连接。该状态对外部完全不透明。

### 3.4. `QAManager` (问答处理器 - Actor)
*   **角色定位**: 作为独立的 **Actor/Worker**，按需执行的处理单元。
*   **职责**: **只负责**处理一次完整的QA交互。
*   **功能**:
    1.  `handle_qa(question: str)`: 一个自包含的、阻塞式的异步方法。
    2.  **执行流程**:
        a. 接收问题字符串。
        b. 调用LLM服务获取答案。
        c. 将答案文本传递给TTS服务进行播放。
        d. `await` TTS播放完成。
    3.  **无状态**: 它本身是无状态的，每次调用都是一次新的、独立的执行。

### 3.5. `LiveStreamSession` (会话管理器)
*   **职责**: **持有和管理**一个直播会话的所有资源和状态。
*   **功能**:
    1.  持有会话级别的资源：
        - `session_id`: 唯一会话标识符
        - `question_queue`: 用户问题队列
        - `start_time`: 会话开始时间
        - `metrics`: 会话级别的统计数据
    2.  提供会话状态查询接口
    3.  管理会话级别的配置和上下文
*   **设计理念**: 
    - 将资源持有与任务协调分离
    - 使 `LiveStreamController` 成为纯粹的协调器
    - 便于会话级别的扩展（如权限控制、历史记录等）

```python
class LiveStreamSession:
    """直播会话 - 持有一个会话的所有资源"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.question_queue = asyncio.Queue()
        self.start_time = datetime.now()
        self.metrics = SessionMetrics()
        self.context = {}  # 可扩展的上下文存储
        
    def get_status(self) -> Dict[str, Any]:
        """获取会话状态"""
        return {
            "session_id": self.session_id,
            "duration": (datetime.now() - self.start_time).seconds,
            "questions_pending": self.question_queue.qsize(),
            "metrics": self.metrics.to_dict()
        }
        
    async def cleanup(self):
        """清理会话资源"""
        # 清空队列
        while not self.question_queue.empty():
            try:
                self.question_queue.get_nowait()
            except asyncio.QueueEmpty:
                break
```

## 4. 核心交互流程 (Sequence Diagrams)

### 4.1. 场景一: 正常启动直播

```mermaid
sequenceDiagram
    participant User
    participant API
    participant LiveStreamController
    participant MainContentPlayer

    User->>+API: POST /start-streaming (script)
    API->>LiveStreamController: create instance + inject dependencies
    API->>LiveStreamController: await controller.start()
    
    activate LiveStreamController
    Note over LiveStreamController: 创建异步任务 (asyncio.create_task)
    LiveStreamController->>MainContentPlayer: play_task = create_task(player.play())
    LiveStreamController->>LiveStreamController: qa_task = create_task(monitor_questions())
    Note over MainContentPlayer: 后台异步运行中...
    deactivate LiveStreamController
    
    API-->>-User: {"status": "started", "session_id": "xxx"}
```

### 4.2. 场景二: 中断处理用户提问

```mermaid
sequenceDiagram
    participant User
    participant API
    participant LiveStreamController
    participant MainContentPlayer
    participant QAManager

    User->>+API: POST /submit-question (question)
    API->>LiveStreamController: await session.question_queue.put(question)
    API-->>-User: {"status": "submitted"}

    Note over LiveStreamController: monitor_questions() Task 检测到问题
    
    activate LiveStreamController
    LiveStreamController->>MainContentPlayer: await player.pause()
    Note over MainContentPlayer: 设置 pause_event<br/>play() 循环在 await pause_event.wait() 处暂停
    
    LiveStreamController->>+QAManager: await qa_manager.handle_qa(question)
    Note over QAManager: 1. 调用 LLM 获取答案<br/>2. 调用 TTS 生成音频<br/>3. 播放音频直到完成
    QAManager-->>-LiveStreamController: 返回（音频播放完成后）
    
    LiveStreamController->>MainContentPlayer: await player.resume()
    Note over MainContentPlayer: 设置 pause_event<br/>play() 循环继续执行
    deactivate LiveStreamController
```

### 4.3. 服务管理与依赖注入

为了实现更好的解耦和可测试性，我们引入轻量级的服务定位器模式，并利用 FastAPI 的依赖注入系统。

#### 4.3.1. 服务定位器设计

```python
from typing import Dict, Any, TypeVar, Type, Optional
from abc import ABC, abstractmethod
import asyncio

T = TypeVar('T')

class ServiceLocator:
    """轻量级服务定位器，管理服务实例的生命周期"""
    
    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._factories: Dict[Type, callable] = {}
        self._singletons: Dict[Type, Any] = {}
        
    def register_factory(self, service_type: Type[T], factory: callable):
        """注册服务工厂函数"""
        self._factories[service_type] = factory
        
    def register_singleton(self, service_type: Type[T], instance: T):
        """注册单例服务"""
        self._singletons[service_type] = instance
        
    async def get(self, service_type: Type[T]) -> T:
        """获取服务实例"""
        # 1. 检查单例
        if service_type in self._singletons:
            return self._singletons[service_type]
            
        # 2. 使用工厂创建
        if service_type in self._factories:
            factory = self._factories[service_type]
            instance = await factory() if asyncio.iscoroutinefunction(factory) else factory()
            return instance
            
        raise ValueError(f"Service {service_type} not registered")
        
    def clear(self):
        """清理所有服务"""
        self._services.clear()
        self._singletons.clear()

# 全局服务定位器实例
service_locator = ServiceLocator()
```

#### 4.3.2. 依赖注入集成

利用 FastAPI 的 `Depends` 系统实现依赖注入：

```python
from fastapi import Depends
from typing import Optional, Union, AsyncGenerator, List

# 定义服务接口
class IMainContentPlayer(ABC):
    """播放器接口"""
    @abstractmethod
    async def play(self, 
                   sentence_provider: Union[List[str], AsyncGenerator[str, None]]) -> None:
        """
        播放内容
        
        Args:
            sentence_provider: 句子来源，可以是：
                - List[str]: 静态的句子列表（向后兼容）
                - AsyncGenerator[str, None]: 动态的句子生成器（支持动态内容）
        """
        pass
        
    @abstractmethod
    async def pause(self) -> Dict[str, Any]:
        pass
        
    @abstractmethod
    async def resume(self) -> Dict[str, Any]:
        pass

class IQAManager(ABC):
    """QA管理器接口"""
    @abstractmethod
    async def handle_qa(self, question: str) -> Dict[str, Any]:
        pass

# 配置注入
class AppConfig:
    """应用配置"""
    def __init__(self):
        self.tts_api_key = os.getenv("TTS_API_KEY")
        self.llm_api_key = os.getenv("LLM_API_KEY")
        self.tts_endpoint = os.getenv("TTS_ENDPOINT", "wss://tts.example.com")
        self.llm_endpoint = os.getenv("LLM_ENDPOINT", "https://llm.example.com")

# FastAPI 依赖函数
async def get_config() -> AppConfig:
    """获取应用配置"""
    return AppConfig()

async def get_player(config: AppConfig = Depends(get_config)) -> IMainContentPlayer:
    """获取播放器实例"""
    return await service_locator.get(IMainContentPlayer)

async def get_qa_manager(config: AppConfig = Depends(get_config)) -> IQAManager:
    """获取QA管理器实例"""
    return await service_locator.get(IQAManager)

# 服务注册（在应用启动时）
async def setup_services():
    """设置服务工厂"""
    config = AppConfig()
    
    # 注册播放器工厂
    async def create_player():
        player = MainContentPlayer(
            tts_engine=CosyVoiceTTSEngine(
                api_key=config.tts_api_key,
                endpoint=config.tts_endpoint
            )
        )
        await player.initialize()
        return player
        
    service_locator.register_factory(IMainContentPlayer, create_player)
    
    # 注册QA管理器工厂
    async def create_qa_manager():
        return QAManager(
            llm_api_key=config.llm_api_key,
            llm_endpoint=config.llm_endpoint
        )
        
    service_locator.register_factory(IQAManager, create_qa_manager)
```

#### 4.3.3. 控制器的依赖注入

更新后的 `LiveStreamController` 不再负责创建依赖：

```python
class LiveStreamController:
    """主控制器 - 现在通过依赖注入获取服务"""
    
    def __init__(self, 
                 player: IMainContentPlayer,
                 qa_manager: IQAManager,
                 session: 'LiveStreamSession'):
        self.player = player
        self.qa_manager = qa_manager
        self.session = session
        
        # 任务管理
        self.play_task: Optional[asyncio.Task] = None
        self.qa_monitor_task: Optional[asyncio.Task] = None
        
    # 其他方法保持不变...

# 在 API 层使用
@app.post("/start_stream")
async def start_stream(
    script_content: List[str],
    player: IMainContentPlayer = Depends(get_player),
    qa_manager: IQAManager = Depends(get_qa_manager)
):
    """启动直播流"""
    # 创建会话
    session = LiveStreamSession(session_id=str(uuid.uuid4()))
    
    # 创建控制器（依赖已注入）
    controller = LiveStreamController(
        player=player,
        qa_manager=qa_manager,
        session=session
    )
    
    # 存储会话
    active_sessions[session.session_id] = controller
    
    # 启动直播
    await controller.start(script_content)
    
    return {"session_id": session.session_id}
```

#### 4.3.4. 测试友好性

这种设计使得测试变得极其简单：

```python
# 在测试中使用 Mock
async def test_controller_with_mocks():
    # 创建 Mock 服务
    mock_player = MockMainContentPlayer()
    mock_qa_manager = MockQAManager()
    
    # 注册 Mock 到服务定位器
    service_locator.register_singleton(IMainContentPlayer, mock_player)
    service_locator.register_singleton(IQAManager, mock_qa_manager)
    
    # 测试控制器
    session = LiveStreamSession("test-session")
    controller = LiveStreamController(
        player=mock_player,
        qa_manager=mock_qa_manager,
        session=session
    )
    
    # 执行测试...
    await controller.start(["测试脚本"])
    
    # 验证 Mock 调用
    assert mock_player.play_called
    assert mock_qa_manager.handle_qa_called == 0
```

这种架构带来的好处：
1. **解耦**: Controller 不依赖具体实现，只依赖接口
2. **可测试**: 轻松替换为 Mock 实现进行单元测试
3. **配置管理**: 统一的配置注入，避免层层传递
4. **生命周期管理**: 服务的创建和销毁由容器统一管理

## 5. 状态管理

新架构的状态管理极其简单，遵循以下原则：

1.  **无全局状态**: 放弃了需要序列化的全局状态对象。
2.  **实例状态**: 状态被封装在 `LiveStreamController` 的实例属性中。这包括对播放器实例的引用、任务的引用以及问题队列。
3.  **异步原语协调**: 任务间的协调（如暂停/恢复）通过 `asyncio.Event` 完成，任务间的数据传递通过 `asyncio.Queue` 完成。这比复杂的事件总线更直接、更轻量，完全满足当前需求。

## 6. 结论：为何此方案更优？

此设计方案是对先前架构的根本性纠正，其优势在于：

*   **简单性**: 代码逻辑直观，遵循Python开发者熟悉的异步编程模式。维护成本和心智负担极低。
*   **响应性**: 后台任务并发执行，互不阻塞。提交问题可以立即被监听任务捕获并进入协调流程，系统响应迅速。
*   **健壮性**: 明确分离了"协调"和"执行"，避免了状态同步问题。每个组件都可以被独立测试。
*   **性能**: 没有重量级的序列化/反序列化操作，所有协调都在内存中通过高效的异步原语完成。

## 7. 错误处理策略

鉴于直播系统的实时性要求，我们需要对各种异常情况制定明确的处理策略，确保系统在面临外部服务故障时仍能提供基本功能。

### 7.1. 错误分类与处理原则

#### 7.1.1. 关键路径错误（立即失败）
这类错误会影响系统核心功能，应该立即停止流程并通知用户：

*   **配置错误**: TTS/LLM服务配置缺失或无效
*   **网络连接失败**: 完全无法连接到核心服务
*   **资源耗尽**: 内存不足、文件描述符耗尽等

**处理策略**: 立即抛出异常，记录详细错误信息，优雅关闭系统

#### 7.1.2. 服务级错误（重试与降级）
这类错误是暂时性的，可以通过重试或降级处理：

*   **TTS服务超时**: 单次合成请求超时
*   **LLM服务限流**: API调用频率限制
*   **网络波动**: 间歇性连接问题

**处理策略**: 
```python
class ErrorHandler:
    async def handle_tts_error(self, text: str, error: Exception) -> AudioResult:
        """TTS错误处理策略"""
        # 1. 指数退避重试
        for attempt in range(3):
            try:
                await asyncio.sleep(0.5 * (2 ** attempt))
                return await self.tts_service.synthesize(text)
            except TimeoutError:
                if attempt == 2:  # 最后一次重试失败
                    # 2. 降级处理：使用预录制音频或跳过
                    logger.warning(f"TTS failed after 3 attempts: {text[:50]}...")
                    return self._get_fallback_audio("tts_unavailable.wav")
        
    async def handle_llm_error(self, question: str) -> str:
        """LLM错误处理策略"""
        try:
            return await self.llm_service.invoke(question, timeout=10)
        except TimeoutError:
            # 使用预设回答模板
            return self._get_template_answer(question)
        except RateLimitError:
            # 提示用户稍后重试
            return "抱歉，服务繁忙，请稍后再试这个问题。"
```

#### 7.1.3. 用户级错误（优雅处理）
这类错误不影响系统运行，但需要向用户提供反馈：

*   **无效问题**: 空内容或格式错误
*   **队列满**: 问题提交过于频繁
*   **重复问题**: 相同问题短时间内重复提交

**处理策略**: 返回友好错误消息，不中断主流程

### 7.2. 具体实现方案

#### 7.2.1. LiveStreamController 错误处理
```python
class LiveStreamController:
    async def _interrupt_and_handle_qa(self, question: str):
        """带错误恢复的QA处理流程"""
        try:
            # 1. 暂停播放
            await self.player.pause()
            
            # 2. 处理QA（可能失败）
            await self.qa_manager.handle_qa(question)
            
        except Exception as e:
            logger.error(f"QA处理失败: {e}")
            # 播放错误提示音频
            await self._play_error_message("处理问题时出现了错误，请稍后重试。")
        finally:
            # 3. 无论成功失败都要恢复播放
            await self.player.resume()
    
    async def _handle_player_crash(self, error: Exception):
        """播放器崩溃恢复"""
        logger.error(f"播放器崩溃: {error}")
        
        # 1. 重新创建播放器实例
        self.player = MainContentPlayer()
        
        # 2. 从上次位置恢复播放
        await self.player.resume_from(self._last_position)
        
        # 3. 重新启动播放任务
        self.play_task = asyncio.create_task(self.player.play(self.script))
```

#### 7.2.2. 网络异常处理
```python
class NetworkErrorHandler:
    def __init__(self):
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=30
        )
    
    async def call_with_circuit_breaker(self, service_call, *args, **kwargs):
        """熔断器模式调用外部服务"""
        if self.circuit_breaker.is_open():
            raise ServiceUnavailableError("服务暂时不可用")
        
        try:
            result = await service_call(*args, **kwargs)
            self.circuit_breaker.record_success()
            return result
        except Exception as e:
            self.circuit_breaker.record_failure()
            raise
```

### 7.3. 错误监控与报告

```python
class ErrorMonitor:
    def __init__(self):
        self.error_counts = defaultdict(int)
        self.error_history = deque(maxlen=100)
    
    def record_error(self, error_type: str, details: dict):
        """记录错误并触发告警"""
        self.error_counts[error_type] += 1
        self.error_history.append({
            "type": error_type,
            "details": details,
            "timestamp": datetime.now(),
            "session_context": self._capture_context()
        })
        
        # 错误率检查
        if self._should_alert(error_type):
            self._trigger_alert(error_type, details)
```

### 7.4. 具体故障场景与恢复策略

下表定义了不同故障场景的处理方式和恢复策略：

| 故障场景 | 组件 | 行为 | 恢复策略 |
|---------|------|------|----------|
| **TTS连接失败（播放前）** | `MainContentPlayer` | 初始化阶段异常 | `Controller` 捕获初始化异常，记录错误日志，返回启动失败响应给API层 |
| **TTS连接中断（播放中）** | `MainContentPlayer` | `play_task` 抛出 `ConnectionError` | `Controller` 捕获异常，执行紧急停机流程：<br>1. 记录严重错误<br>2. 取消所有活动任务<br>3. 通知前端连接已断开<br>4. 清理会话资源 |
| **LLM调用超时** | `QAManager` | `handle_qa` 抛出 `TimeoutError` | `Controller` 捕获异常，执行降级处理：<br>1. 播放预设的道歉音频<br>2. 记录超时事件<br>3. 恢复主内容播放<br>4. 不终止直播流程 |
| **音频生成失败（单句）** | `MainContentPlayer` | TTS返回错误 | `Player` 内部处理：<br>1. 记录失败的句子<br>2. 跳过该句，继续下一句<br>3. 如果连续3句失败，抛出异常 |
| **问题格式错误** | API Layer | JSON解析失败 | API层直接返回 `400 Bad Request`，不影响后台服务 |
| **队列溢出** | `LiveStreamSession` | 问题队列满 | `question_queue.put_nowait()` 抛出 `QueueFull`：<br>1. API层返回 `503 Service Unavailable`<br>2. 提示用户稍后重试<br>3. 记录队列溢出事件 |
| **内存不足** | System | `MemoryError` | 全局异常处理器：<br>1. 触发紧急清理流程<br>2. 停止接受新会话<br>3. 逐步关闭现有会话<br>4. 发送系统告警 |
| **网络抖动** | TTS/LLM Services | 临时网络错误 | 使用指数退避重试：<br>1. 第1次：等待 0.5秒<br>2. 第2次：等待 1秒<br>3. 第3次：等待 2秒<br>4. 超过3次视为永久失败 |

#### 7.4.1. 预设音频资源

为了支持降级处理，系统预先准备以下音频资源：

```python
class FallbackAudioManager:
    """降级音频管理器"""
    
    AUDIO_RESOURCES = {
        "qa_timeout": "assets/audio/qa_timeout_apology.mp3",
        "system_error": "assets/audio/system_error_notice.mp3",
        "tts_failure": "assets/audio/tts_failure_notice.mp3",
        "goodbye": "assets/audio/stream_ending.mp3"
    }
    
    async def play_fallback(self, scenario: str) -> bool:
        """播放降级音频"""
        audio_path = self.AUDIO_RESOURCES.get(scenario)
        if audio_path and os.path.exists(audio_path):
            # 直接播放本地音频文件
            return await self._play_local_audio(audio_path)
        return False
```

#### 7.4.2. 错误恢复决策树

```python
async def handle_task_failure(self, task: asyncio.Task, error: Exception):
    """统一的任务失败处理逻辑"""
    
    if isinstance(error, asyncio.CancelledError):
        # 正常取消，无需处理
        return
        
    elif isinstance(error, (ConnectionError, WebSocketError)):
        # 连接类错误 - 严重故障
        await self.emergency_shutdown(reason="Connection lost")
        
    elif isinstance(error, TimeoutError):
        # 超时错误 - 可恢复
        if task == self.qa_task:
            await self.fallback_audio.play_fallback("qa_timeout")
            await self.player.resume()
        else:
            # 主播放超时，需要重启
            await self.restart_playback()
            
    elif isinstance(error, TTSError):
        # TTS错误 - 根据错误计数决定
        self.tts_error_count += 1
        if self.tts_error_count > 3:
            await self.emergency_shutdown(reason="TTS service failure")
        else:
            logger.warning(f"TTS错误 {self.tts_error_count}/3，尝试继续")
            
    else:
        # 未知错误 - 记录并继续
        logger.error(f"未知错误: {error}")
        self.error_monitor.record_error("unknown", {"error": str(error)})
```

## 8. 并发安全控制

在多任务异步环境中，我们需要确保并发操作的安全性，避免竞态条件和资源冲突。

### 8.1. 并发风险识别

#### 8.1.1. 问题队列竞态条件
**风险**: 多个用户同时提交问题，可能导致队列状态不一致或丢失问题

**解决方案**: 使用线程安全的 `asyncio.Queue`
```python
class LiveStreamController:
    def __init__(self):
        # asyncio.Queue 是协程安全的，无需额外同步
        self.question_queue = asyncio.Queue(maxsize=10)  # 限制队列大小
        
    async def submit_question(self, question: str) -> dict:
        """线程安全的问题提交"""
        try:
            # 非阻塞放入，避免队列满时阻塞API响应
            self.question_queue.put_nowait({
                "text": question,
                "timestamp": datetime.now(),
                "id": str(uuid.uuid4())
            })
            return {"success": True, "queue_size": self.question_queue.qsize()}
        except asyncio.QueueFull:
            return {"success": False, "error": "问题队列已满，请稍后重试"}
```

#### 8.1.2. QA处理重入问题
**风险**: QA处理过程中又收到新问题，可能导致多个QA并发执行

**解决方案**: 使用异步锁确保QA处理的串行化
```python
class LiveStreamController:
    def __init__(self):
        self._qa_lock = asyncio.Lock()  # QA处理锁
        self._is_qa_active = False      # QA状态标记
        
    async def _interrupt_and_handle_qa(self, question: dict):
        """确保QA处理串行化"""
        async with self._qa_lock:
            if self._is_qa_active:
                logger.warning("QA处理已在进行中，跳过重复请求")
                return
                
            self._is_qa_active = True
            try:
                await self.player.pause()
                await self.qa_manager.handle_qa(question["text"])
            finally:
                self._is_qa_active = False
                await self.player.resume()
```

#### 8.1.3. 播放器状态同步
**风险**: 暂停/恢复操作的竞态条件，可能导致播放状态错乱

**解决方案**: 在播放器内部使用状态锁
```python
class MainContentPlayer:
    def __init__(self):
        self._state_lock = asyncio.Lock()
        self._is_paused = False
        self._pause_event = asyncio.Event()
        self._pause_event.set()  # 初始为非暂停状态
        
    async def pause(self):
        """线程安全的暂停操作"""
        async with self._state_lock:
            if not self._is_paused:
                self._is_paused = True
                self._pause_event.clear()
                logger.info("播放器已暂停")
                
    async def resume(self):
        """线程安全的恢复操作"""
        async with self._state_lock:
            if self._is_paused:
                self._is_paused = False
                self._pause_event.set()
                logger.info("播放器已恢复")
```

### 8.2. 并发控制机制

#### 8.2.1. 问题处理流水线
为了提高系统吞吐量同时保证安全性，我们设计一个流水线处理机制：

```python
class QAPipeline:
    def __init__(self, max_concurrent_qa: int = 1):
        self.semaphore = asyncio.Semaphore(max_concurrent_qa)
        self.processing_queue = asyncio.Queue()
        
    async def process_questions(self):
        """QA处理工作者"""
        while True:
            question = await self.processing_queue.get()
            async with self.semaphore:
                try:
                    await self._handle_single_qa(question)
                except Exception as e:
                    logger.error(f"QA处理异常: {e}")
                finally:
                    self.processing_queue.task_done()
```

#### 8.2.2. 优雅关闭机制
确保系统关闭时所有任务都能正确完成或取消：

```python
class LiveStreamController:
    async def shutdown(self):
        """优雅关闭控制器"""
        logger.info("开始优雅关闭直播控制器...")
        
        # 1. 停止接收新问题
        self._shutdown_flag = True
        
        # 2. 等待当前QA处理完成
        if self._is_qa_active:
            logger.info("等待当前QA处理完成...")
            await asyncio.wait_for(self._wait_qa_complete(), timeout=30)
            
        # 3. 取消所有后台任务
        tasks_to_cancel = [self.play_task, self.qa_monitor_task]
        for task in tasks_to_cancel:
            if task and not task.done():
                task.cancel()
                
        # 4. 等待任务完成取消
        await asyncio.gather(*tasks_to_cancel, return_exceptions=True)
        
        # 5. 清理资源
        await self.player.stop()
        await self.qa_manager.cleanup()
        
        logger.info("直播控制器已优雅关闭")
```

### 8.3. 性能监控与调优

#### 8.3.1. 并发性能指标
```python
class ConcurrencyMonitor:
    def __init__(self):
        self.metrics = {
            "queue_size_history": deque(maxlen=100),
            "qa_processing_times": deque(maxlen=50),
            "concurrent_operations": 0
        }
    
    def record_queue_size(self):
        """记录队列大小变化"""
        size = self.controller.question_queue.qsize()
        self.metrics["queue_size_history"].append((datetime.now(), size))
        
    def record_qa_processing_time(self, duration: float):
        """记录QA处理时间"""
        self.metrics["qa_processing_times"].append(duration)
        
    def get_performance_summary(self) -> dict:
        """获取性能摘要"""
        processing_times = list(self.metrics["qa_processing_times"])
        return {
            "avg_queue_size": self._calculate_avg_queue_size(),
            "avg_qa_time": sum(processing_times) / len(processing_times) if processing_times else 0,
            "max_qa_time": max(processing_times) if processing_times else 0,
            "queue_backlog": self.controller.question_queue.qsize()
        }
```

## 9. 资源清理机制

在异步环境中，正确的资源清理对系统稳定性至关重要。我们需要确保所有资源（连接、任务、内存等）在适当的时候被正确释放。

### 9.1. 资源分类与清理策略

#### 9.1.1. 网络连接资源
**包含资源**: TTS WebSocket连接、LLM HTTP连接、音频流连接

**清理策略**: 
```python
class ConnectionManager:
    def __init__(self):
        self.active_connections = set()
        self._cleanup_lock = asyncio.Lock()
        
    async def register_connection(self, connection):
        """注册活跃连接"""
        async with self._cleanup_lock:
            self.active_connections.add(connection)
            
    async def cleanup_connection(self, connection):
        """清理单个连接"""
        try:
            if hasattr(connection, 'close'):
                await connection.close()
            elif hasattr(connection, 'disconnect'):
                await connection.disconnect()
        except Exception as e:
            logger.warning(f"连接清理异常: {e}")
        finally:
            async with self._cleanup_lock:
                self.active_connections.discard(connection)
                
    async def cleanup_all(self):
        """清理所有连接"""
        connections_to_cleanup = list(self.active_connections)
        logger.info(f"开始清理 {len(connections_to_cleanup)} 个活跃连接")
        
        cleanup_tasks = [
            self.cleanup_connection(conn) for conn in connections_to_cleanup
        ]
        
        # 并发清理，但设置超时
        try:
            await asyncio.wait_for(
                asyncio.gather(*cleanup_tasks, return_exceptions=True),
                timeout=10.0
            )
        except asyncio.TimeoutError:
            logger.warning("连接清理超时，强制结束")
```

#### 9.1.2. 异步任务资源
**包含资源**: 播放任务、QA监听任务、监控任务

**清理策略**:
```python
class TaskManager:
    def __init__(self):
        self.active_tasks = {}  # task_name -> task
        self._shutdown_event = asyncio.Event()
        
    def register_task(self, name: str, task: asyncio.Task):
        """注册任务"""
        self.active_tasks[name] = task
        # 为任务添加完成回调
        task.add_done_callback(lambda t: self._on_task_done(name, t))
        
    def _on_task_done(self, name: str, task: asyncio.Task):
        """任务完成回调"""
        self.active_tasks.pop(name, None)
        if task.cancelled():
            logger.info(f"任务 {name} 已被取消")
        elif task.exception():
            logger.error(f"任务 {name} 异常结束: {task.exception()}")
        else:
            logger.info(f"任务 {name} 正常完成")
            
    async def cancel_task(self, name: str, timeout: float = 5.0):
        """取消单个任务"""
        task = self.active_tasks.get(name)
        if not task or task.done():
            return
            
        logger.info(f"取消任务: {name}")
        task.cancel()
        
        try:
            await asyncio.wait_for(task, timeout=timeout)
        except (asyncio.CancelledError, asyncio.TimeoutError):
            pass  # 期望的结果
        except Exception as e:
            logger.warning(f"任务 {name} 取消时异常: {e}")
            
    async def cancel_all_tasks(self, timeout: float = 10.0):
        """取消所有任务"""
        if not self.active_tasks:
            return
            
        logger.info(f"开始取消 {len(self.active_tasks)} 个活跃任务")
        
        # 设置关闭标志
        self._shutdown_event.set()
        
        # 取消所有任务
        for task in self.active_tasks.values():
            if not task.done():
                task.cancel()
                
        # 等待所有任务完成取消
        remaining_tasks = [t for t in self.active_tasks.values() if not t.done()]
        if remaining_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*remaining_tasks, return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"任务取消超时，强制结束剩余 {len(remaining_tasks)} 个任务")
```

#### 9.1.3. 内存缓存资源
**包含资源**: 音频缓冲区、问题队列、历史记录

**清理策略**:
```python
class MemoryManager:
    def __init__(self):
        self.audio_buffers = {}
        self.question_history = deque(maxlen=100)
        self.cache_data = {}
        
    def clear_audio_buffers(self):
        """清理音频缓冲区"""
        buffer_count = len(self.audio_buffers)
        total_size = sum(len(buffer) for buffer in self.audio_buffers.values())
        
        self.audio_buffers.clear()
        logger.info(f"已清理 {buffer_count} 个音频缓冲区，释放 {total_size / 1024:.1f} KB 内存")
        
    def clear_question_queue(self, question_queue: asyncio.Queue):
        """清理问题队列"""
        cleared_count = 0
        while not question_queue.empty():
            try:
                question_queue.get_nowait()
                cleared_count += 1
            except asyncio.QueueEmpty:
                break
                
        logger.info(f"已清理问题队列中的 {cleared_count} 个待处理问题")
        
    def cleanup_all(self, question_queue: asyncio.Queue = None):
        """清理所有内存资源"""
        self.clear_audio_buffers()
        if question_queue:
            self.clear_question_queue(question_queue)
        self.question_history.clear()
        self.cache_data.clear()
        
        # 强制垃圾回收
        import gc
        gc.collect()
        logger.info("内存清理完成")
```

### 9.2. 完整的清理流程

#### 9.2.1. LiveStreamController 的资源清理
```python
class LiveStreamController:
    def __init__(self):
        self.task_manager = TaskManager()
        self.connection_manager = ConnectionManager()
        self.memory_manager = MemoryManager()
        self._cleanup_completed = asyncio.Event()
        
    async def start(self, script_content: List[str]):
        """启动直播流"""
        try:
            # 启动播放任务
            play_task = asyncio.create_task(self.player.play(script_content))
            self.task_manager.register_task("play_content", play_task)
            
            # 启动QA监听任务
            qa_task = asyncio.create_task(self.monitor_questions())
            self.task_manager.register_task("qa_monitor", qa_task)
            
            # 注册连接
            await self.connection_manager.register_connection(self.player.tts_connection)
            
            logger.info("直播流已启动")
            
        except Exception as e:
            logger.error(f"启动失败: {e}")
            await self.cleanup()
            raise
            
    async def cleanup(self):
        """完整的资源清理流程"""
        if self._cleanup_completed.is_set():
            logger.info("资源已清理，跳过重复清理")
            return
            
        logger.info("开始资源清理流程...")
        
        try:
            # 1. 停止接收新请求
            self._shutdown_flag = True
            
            # 2. 清理任务（优先级最高）
            await self.task_manager.cancel_all_tasks(timeout=15.0)
            
            # 3. 清理网络连接
            await self.connection_manager.cleanup_all()
            
            # 4. 清理播放器资源
            if hasattr(self, 'player') and self.player:
                await self.player.cleanup()
                
            # 5. 清理QA管理器资源
            if hasattr(self, 'qa_manager') and self.qa_manager:
                await self.qa_manager.cleanup()
                
            # 6. 清理内存资源
            self.memory_manager.cleanup_all(self.question_queue)
            
            # 7. 标记清理完成
            self._cleanup_completed.set()
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理过程中出现异常: {e}")
            raise
        
    async def __aenter__(self):
        """支持 async with 语法"""
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """自动资源清理"""
        await self.cleanup()
```

#### 9.2.2. 组件级资源清理
```python
class MainContentPlayer:
    async def cleanup(self):
        """播放器资源清理"""
        logger.info("清理播放器资源...")
        
        try:
            # 1. 停止所有播放
            await self.stop()
            
            # 2. 关闭TTS连接
            if self.tts_engine:
                await self.tts_engine.disconnect()
                
            # 3. 清理音频缓冲区
            if hasattr(self, 'audio_buffer'):
                self.audio_buffer.clear()
                
            # 4. 清理句子队列
            if hasattr(self, 'sentence_queue'):
                while not self.sentence_queue.empty():
                    try:
                        self.sentence_queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break
                        
        except Exception as e:
            logger.error(f"播放器资源清理异常: {e}")

class QAManager:
    async def cleanup(self):
        """QA管理器资源清理"""
        logger.info("清理QA管理器资源...")
        
        try:
            # 1. 关闭LLM连接
            if hasattr(self, 'llm_client'):
                await self.llm_client.close()
                
            # 2. 清理缓存
            if hasattr(self, 'answer_cache'):
                self.answer_cache.clear()
                
        except Exception as e:
            logger.error(f"QA管理器资源清理异常: {e}")
```

### 9.3. 监控与告警

#### 9.3.1. 资源泄露监控
```python
class ResourceLeakMonitor:
    def __init__(self):
        self.resource_snapshots = {}
        
    def take_snapshot(self, name: str):
        """记录资源快照"""
        import psutil
        import threading
        
        process = psutil.Process()
        self.resource_snapshots[name] = {
            "timestamp": datetime.now(),
            "memory_mb": process.memory_info().rss / 1024 / 1024,
            "open_files": len(process.open_files()),
            "threads": threading.active_count(),
            "connections": len(process.connections())
        }
        
    def check_for_leaks(self, before: str, after: str) -> dict:
        """检查资源泄露"""
        if before not in self.resource_snapshots or after not in self.resource_snapshots:
            return {}
            
        before_snap = self.resource_snapshots[before]
        after_snap = self.resource_snapshots[after]
        
        leak_report = {}
        for key in ["memory_mb", "open_files", "threads", "connections"]:
            diff = after_snap[key] - before_snap[key]
            if diff > 0:  # 资源增加
                leak_report[key] = {
                    "before": before_snap[key],
                    "after": after_snap[key],
                    "diff": diff
                }
                
        return leak_report
```

## 10. 状态查询接口设计

为了支持前端监控、系统调试和运维管理，我们需要提供丰富的状态查询接口。这些接口应该是只读的、高性能的，不影响主业务流程。

### 10.1. API接口设计

#### 10.1.1. 基础状态查询
```python
@router.get("/api/status")
async def get_basic_status():
    """获取基础系统状态"""
    if not controller or not controller.is_active():
        return {
            "status": "stopped",
            "message": "直播流未启动",
            "timestamp": datetime.now().isoformat()
        }
    
    try:
        status_data = {
            "status": "running",
            "session_id": controller.session_id,
            "uptime_seconds": controller.get_uptime(),
            "is_playing": controller.player.is_playing(),
            "is_paused": controller.player.is_paused(),
            "current_progress": controller.get_playback_progress(),
            "queue_status": {
                "pending_questions": controller.question_queue.qsize(),
                "is_qa_active": controller.is_qa_processing(),
                "last_qa_time": controller.get_last_qa_time()
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return {"success": True, "data": status_data}
        
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        return {"success": False, "error": str(e)}
```

#### 10.1.2. 详细性能指标
```python
@router.get("/api/metrics")
async def get_performance_metrics():
    """获取详细性能指标"""
    try:
        # 从监控器获取数据
        concurrency_metrics = controller.concurrency_monitor.get_performance_summary()
        error_metrics = controller.error_monitor.get_error_summary()
        resource_metrics = controller.resource_monitor.get_resource_usage()
        
        metrics = {
            "performance": {
                "avg_response_time_ms": concurrency_metrics.get("avg_qa_time", 0) * 1000,
                "max_response_time_ms": concurrency_metrics.get("max_qa_time", 0) * 1000,
                "questions_processed_total": controller.get_total_questions_processed(),
                "questions_per_minute": controller.get_questions_per_minute(),
                "queue_wait_time_avg_ms": concurrency_metrics.get("avg_queue_wait", 0) * 1000
            },
            "errors": {
                "error_rate_5min": error_metrics.get("error_rate_5min", 0),
                "total_errors": error_metrics.get("total_errors", 0),
                "last_error_time": error_metrics.get("last_error_time"),
                "error_breakdown": error_metrics.get("error_types", {})
            },
            "resources": {
                "memory_usage_mb": resource_metrics.get("memory_mb", 0),
                "cpu_usage_percent": resource_metrics.get("cpu_percent", 0),
                "active_connections": resource_metrics.get("connections", 0),
                "active_tasks": resource_metrics.get("tasks", 0)
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return {"success": True, "data": metrics}
        
    except Exception as e:
        logger.error(f"获取指标失败: {e}")
        return {"success": False, "error": str(e)}
```

#### 10.1.3. 播放进度查询
```python
@router.get("/api/playback")
async def get_playback_status():
    """获取播放状态详情"""
    try:
        if not controller or not controller.player:
            return {"success": False, "error": "播放器未初始化"}
            
        playback_info = {
            "is_playing": controller.player.is_playing(),
            "is_paused": controller.player.is_paused(),
            "current_sentence": controller.player.get_current_sentence_index(),
            "total_sentences": controller.player.get_total_sentences(),
            "progress_percent": controller.player.get_progress_percentage(),
            "estimated_remaining_minutes": controller.player.get_estimated_remaining_time(),
            "playback_speed": controller.player.get_playback_speed(),
            "audio_buffer_status": {
                "buffered_seconds": controller.player.get_buffered_duration(),
                "buffer_health": controller.player.get_buffer_health()
            }
        }
        
        return {"success": True, "data": playback_info}
        
    except Exception as e:
        logger.error(f"获取播放状态失败: {e}")
        return {"success": False, "error": str(e)}
```

#### 10.1.4. 问题队列状态
```python
@router.get("/api/questions")
async def get_question_queue_status():
    """获取问题队列状态"""
    try:
        queue_info = {
            "queue_size": controller.question_queue.qsize(),
            "max_queue_size": controller.question_queue.maxsize,
            "is_full": controller.question_queue.full(),
            "processing_status": {
                "is_qa_active": controller.is_qa_processing(),
                "current_question_id": controller.get_current_qa_id(),
                "processing_start_time": controller.get_qa_start_time(),
                "estimated_completion_time": controller.get_qa_estimated_completion()
            },
            "recent_questions": controller.get_recent_questions_summary(limit=5),
            "queue_metrics": {
                "questions_processed_today": controller.get_daily_question_count(),
                "avg_processing_time_seconds": controller.get_avg_processing_time(),
                "success_rate_percent": controller.get_qa_success_rate()
            }
        }
        
        return {"success": True, "data": queue_info}
        
    except Exception as e:
        logger.error(f"获取队列状态失败: {e}")
        return {"success": False, "error": str(e)}
```

### 10.2. 实时状态更新

#### 10.2.1. WebSocket 状态推送
```python
from fastapi import WebSocket, WebSocketDisconnect
from typing import Set

class StatusBroadcaster:
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.broadcast_task = None
        
    async def connect(self, websocket: WebSocket):
        """新客户端连接"""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"新的状态监听连接，当前连接数: {len(self.active_connections)}")
        
    def disconnect(self, websocket: WebSocket):
        """客户端断开"""
        self.active_connections.discard(websocket)
        logger.info(f"状态监听连接断开，当前连接数: {len(self.active_connections)}")
        
    async def broadcast_status_update(self, status_data: dict):
        """广播状态更新"""
        if not self.active_connections:
            return
            
        message = json.dumps({
            "type": "status_update",
            "data": status_data,
            "timestamp": datetime.now().isoformat()
        })
        
        # 并发发送给所有连接的客户端
        disconnect_list = []
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except WebSocketDisconnect:
                disconnect_list.append(connection)
            except Exception as e:
                logger.warning(f"状态广播失败: {e}")
                disconnect_list.append(connection)
        
        # 清理断开的连接
        for connection in disconnect_list:
            self.disconnect(connection)

@router.websocket("/ws/status")
async def websocket_status_endpoint(websocket: WebSocket):
    """WebSocket状态推送端点"""
    await status_broadcaster.connect(websocket)
    
    try:
        while True:
            # 保持连接活跃，等待客户端消息
            await websocket.receive_text()
            
    except WebSocketDisconnect:
        status_broadcaster.disconnect(websocket)
```

#### 10.2.2. 定时状态推送
```python
class StatusUpdateScheduler:
    def __init__(self, broadcaster: StatusBroadcaster):
        self.broadcaster = broadcaster
        self.update_task = None
        self.update_interval = 2.0  # 2秒推送一次
        
    async def start_periodic_updates(self):
        """启动定时状态推送"""
        self.update_task = asyncio.create_task(self._periodic_update_loop())
        
    async def stop_periodic_updates(self):
        """停止定时状态推送"""
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
                
    async def _periodic_update_loop(self):
        """定时推送循环"""
        while True:
            try:
                if self.broadcaster.active_connections and controller:
                    # 收集状态数据
                    status_data = {
                        "playback": {
                            "is_playing": controller.player.is_playing(),
                            "progress_percent": controller.player.get_progress_percentage(),
                            "current_sentence": controller.player.get_current_sentence_index()
                        },
                        "queue": {
                            "size": controller.question_queue.qsize(),
                            "is_qa_active": controller.is_qa_processing()
                        },
                        "system": {
                            "uptime": controller.get_uptime(),
                            "error_count": controller.error_monitor.get_recent_error_count()
                        }
                    }
                    
                    # 广播状态更新
                    await self.broadcaster.broadcast_status_update(status_data)
                    
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"状态推送异常: {e}")
                await asyncio.sleep(self.update_interval)
```

### 10.3. 健康检查接口

#### 10.3.1. 服务健康检查
```python
@router.get("/api/health")
async def health_check():
    """服务健康检查"""
    health_status = {
        "status": "healthy",
        "checks": {},
        "timestamp": datetime.now().isoformat()
    }
    
    # 检查控制器状态
    if controller and controller.is_active():
        health_status["checks"]["controller"] = "healthy"
    else:
        health_status["checks"]["controller"] = "unhealthy"
        health_status["status"] = "unhealthy"
    
    # 检查播放器状态
    try:
        if controller and controller.player:
            player_healthy = await controller.player.health_check()
            health_status["checks"]["player"] = "healthy" if player_healthy else "unhealthy"
        else:
            health_status["checks"]["player"] = "not_initialized"
    except Exception as e:
        health_status["checks"]["player"] = f"error: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # 检查QA管理器状态
    try:
        if controller and controller.qa_manager:
            qa_healthy = await controller.qa_manager.health_check()
            health_status["checks"]["qa_manager"] = "healthy" if qa_healthy else "unhealthy"
        else:
            health_status["checks"]["qa_manager"] = "not_initialized"
    except Exception as e:
        health_status["checks"]["qa_manager"] = f"error: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # 检查外部服务连接
    health_status["checks"]["external_services"] = await check_external_services_health()
    
    # 设置HTTP状态码
    status_code = 200 if health_status["status"] == "healthy" else 503
    
    return JSONResponse(content=health_status, status_code=status_code)

async def check_external_services_health() -> dict:
    """检查外部服务健康状态"""
    service_checks = {}
    
    # 检查TTS服务
    try:
        # 发送简单的健康检查请求
        response = await tts_service.health_check(timeout=5.0)
        service_checks["tts"] = "healthy" if response else "unhealthy"
    except Exception as e:
        service_checks["tts"] = f"error: {str(e)}"
    
    # 检查LLM服务
    try:
        response = await llm_service.health_check(timeout=5.0)
        service_checks["llm"] = "healthy" if response else "unhealthy"
    except Exception as e:
        service_checks["llm"] = f"error: {str(e)}"
    
    return service_checks
```

## 11. 性能优化考虑

虽然新架构的核心理念是简单性，但在实时音频流处理场景中，我们仍需要关注几个关键的性能优化点，确保系统能够提供流畅的用户体验。

### 11.1. 音频处理优化

#### 11.1.1. TTS预加载策略
```python
class TTSPreloader:
    def __init__(self, preload_sentences: int = 3):
        self.preload_count = preload_sentences
        self.audio_cache = {}
        self.preload_task = None
        
    async def start_preloading(self, sentences: List[str], current_index: int):
        """启动预加载任务"""
        if self.preload_task and not self.preload_task.done():
            self.preload_task.cancel()
            
        self.preload_task = asyncio.create_task(
            self._preload_next_sentences(sentences, current_index)
        )
        
    async def _preload_next_sentences(self, sentences: List[str], current_index: int):
        """预加载接下来的几句"""
        try:
            # 计算需要预加载的句子范围
            start_idx = current_index + 1
            end_idx = min(start_idx + self.preload_count, len(sentences))
            
            preload_tasks = []
            for i in range(start_idx, end_idx):
                if i not in self.audio_cache:
                    task = asyncio.create_task(self._synthesize_and_cache(i, sentences[i]))
                    preload_tasks.append(task)
            
            # 并发执行预加载
            if preload_tasks:
                await asyncio.gather(*preload_tasks, return_exceptions=True)
                logger.info(f"预加载完成: {len(preload_tasks)} 个句子")
                
        except asyncio.CancelledError:
            logger.info("预加载任务被取消")
        except Exception as e:
            logger.error(f"预加载异常: {e}")
            
    async def _synthesize_and_cache(self, index: int, sentence: str):
        """合成并缓存音频"""
        try:
            audio_data = await self.tts_service.synthesize(sentence)
            self.audio_cache[index] = {
                "audio": audio_data,
                "cached_at": datetime.now(),
                "size": len(audio_data)
            }
        except Exception as e:
            logger.warning(f"句子 {index} 预加载失败: {e}")
            
    def get_cached_audio(self, index: int) -> Optional[bytes]:
        """获取缓存的音频"""
        cached_item = self.audio_cache.get(index)
        if cached_item:
            return cached_item["audio"]
        return None
        
    def cleanup_old_cache(self, current_index: int):
        """清理旧的缓存"""
        # 只保留当前句子之后的缓存
        keys_to_remove = [k for k in self.audio_cache.keys() if k <= current_index - 2]
        for key in keys_to_remove:
            del self.audio_cache[key]
```

#### 11.1.2. 音频缓冲优化
```python
class AudioBufferManager:
    def __init__(self, buffer_size_seconds: float = 10.0):
        self.buffer_size = buffer_size_seconds
        self.buffer = deque()
        self.buffer_lock = asyncio.Lock()
        self.buffer_event = asyncio.Event()
        
    async def add_audio_chunk(self, chunk: AudioChunk):
        """添加音频块到缓冲区"""
        async with self.buffer_lock:
            self.buffer.append(chunk)
            
            # 维护缓冲区大小
            total_duration = sum(chunk.duration for chunk in self.buffer)
            while total_duration > self.buffer_size and len(self.buffer) > 1:
                removed_chunk = self.buffer.popleft()
                total_duration -= removed_chunk.duration
                
            # 通知有新数据可用
            self.buffer_event.set()
            
    async def get_next_chunk(self) -> Optional[AudioChunk]:
        """获取下一个音频块"""
        while True:
            async with self.buffer_lock:
                if self.buffer:
                    return self.buffer.popleft()
                    
            # 等待新数据
            await self.buffer_event.wait()
            self.buffer_event.clear()
            
    def get_buffer_health(self) -> dict:
        """获取缓冲区健康状态"""
        total_duration = sum(chunk.duration for chunk in self.buffer)
        return {
            "buffer_duration_seconds": total_duration,
            "buffer_fullness_percent": (total_duration / self.buffer_size) * 100,
            "chunk_count": len(self.buffer),
            "is_healthy": total_duration >= 2.0  # 至少2秒缓冲
        }
```

### 11.2. 内存管理优化

#### 11.2.1. 智能内存监控
```python
class MemoryOptimizer:
    def __init__(self, max_memory_mb: int = 500):
        self.max_memory = max_memory_mb
        self.monitoring_task = None
        self.memory_pressure_event = asyncio.Event()
        
    async def start_monitoring(self):
        """启动内存监控"""
        self.monitoring_task = asyncio.create_task(self._memory_monitor_loop())
        
    async def _memory_monitor_loop(self):
        """内存监控循环"""
        import psutil
        
        while True:
            try:
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                
                if memory_mb > self.max_memory:
                    logger.warning(f"内存使用过高: {memory_mb:.1f}MB > {self.max_memory}MB")
                    self.memory_pressure_event.set()
                    await self._trigger_memory_cleanup()
                    
                elif memory_mb > self.max_memory * 0.8:
                    logger.info(f"内存使用接近限制: {memory_mb:.1f}MB")
                    await self._gentle_memory_cleanup()
                    
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"内存监控异常: {e}")
                await asyncio.sleep(30)
                
    async def _trigger_memory_cleanup(self):
        """触发内存清理"""
        # 清理音频缓存
        if hasattr(self, 'tts_preloader'):
            self.tts_preloader.audio_cache.clear()
            
        # 清理历史记录
        if hasattr(self, 'question_history'):
            self.question_history.clear()
            
        # 强制垃圾回收
        import gc
        gc.collect()
        
        logger.info("执行了紧急内存清理")
        
    async def _gentle_memory_cleanup(self):
        """温和的内存清理"""
        # 只清理旧的缓存
        if hasattr(self, 'tts_preloader'):
            self.tts_preloader.cleanup_old_cache(current_index=999)
            
        import gc
        gc.collect()
```

#### 11.2.2. 对象池复用
```python
class AudioChunkPool:
    """音频块对象池，减少频繁的内存分配"""
    
    def __init__(self, pool_size: int = 100):
        self.pool = []
        self.pool_lock = asyncio.Lock()
        self.pool_size = pool_size
        
    async def get_chunk(self) -> AudioChunk:
        """从池中获取音频块"""
        async with self.pool_lock:
            if self.pool:
                return self.pool.pop()
            else:
                return AudioChunk()  # 创建新对象
                
    async def return_chunk(self, chunk: AudioChunk):
        """归还音频块到池中"""
        async with self.pool_lock:
            if len(self.pool) < self.pool_size:
                # 重置对象状态
                chunk.reset()
                self.pool.append(chunk)
```

### 11.3. 网络优化

#### 11.3.1. 连接池管理
```python
class ConnectionPoolManager:
    def __init__(self):
        self.tts_pool = {}
        self.llm_pool = {}
        self.pool_lock = asyncio.Lock()
        
    async def get_tts_connection(self) -> TTSConnection:
        """获取TTS连接"""
        async with self.pool_lock:
            # 寻找空闲连接
            for conn_id, conn in self.tts_pool.items():
                if not conn.is_busy():
                    return conn
                    
            # 创建新连接
            new_conn = await TTSConnection.create()
            self.tts_pool[new_conn.id] = new_conn
            return new_conn
            
    async def release_connection(self, connection):
        """释放连接"""
        connection.mark_idle()
        
    async def cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = datetime.now()
        idle_threshold = timedelta(minutes=5)
        
        async with self.pool_lock:
            idle_connections = [
                (conn_id, conn) for conn_id, conn in self.tts_pool.items()
                if current_time - conn.last_used > idle_threshold
            ]
            
            for conn_id, conn in idle_connections:
                await conn.close()
                del self.tts_pool[conn_id]
                
            if idle_connections:
                logger.info(f"清理了 {len(idle_connections)} 个空闲连接")
```

#### 11.3.2. 请求批处理优化
```python
class BatchProcessor:
    def __init__(self, batch_size: int = 5, timeout_seconds: float = 2.0):
        self.batch_size = batch_size
        self.timeout = timeout_seconds
        self.pending_requests = []
        self.batch_timer = None
        
    async def add_request(self, request_data) -> any:
        """添加请求到批处理队列"""
        future = asyncio.Future()
        self.pending_requests.append((request_data, future))
        
        # 检查是否达到批处理大小
        if len(self.pending_requests) >= self.batch_size:
            await self._process_batch()
        elif self.batch_timer is None:
            # 启动超时定时器
            self.batch_timer = asyncio.create_task(self._timeout_handler())
            
        return await future
        
    async def _timeout_handler(self):
        """超时处理器"""
        try:
            await asyncio.sleep(self.timeout)
            if self.pending_requests:
                await self._process_batch()
        except asyncio.CancelledError:
            pass
        finally:
            self.batch_timer = None
            
    async def _process_batch(self):
        """处理批次"""
        if not self.pending_requests:
            return
            
        batch = self.pending_requests.copy()
        self.pending_requests.clear()
        
        # 取消定时器
        if self.batch_timer:
            self.batch_timer.cancel()
            self.batch_timer = None
            
        try:
            # 批处理请求
            requests = [item[0] for item in batch]
            results = await self._batch_process_requests(requests)
            
            # 返回结果
            for i, (_, future) in enumerate(batch):
                if i < len(results):
                    future.set_result(results[i])
                else:
                    future.set_exception(Exception("批处理结果不足"))
                    
        except Exception as e:
            # 所有请求都失败
            for _, future in batch:
                future.set_exception(e)
```

### 11.4. 性能监控指标

#### 11.4.1. 关键性能指标
```python
class PerformanceMetrics:
    def __init__(self):
        self.metrics = {
            # 延迟指标
            "tts_latency_ms": deque(maxlen=100),
            "qa_response_time_ms": deque(maxlen=100),
            "audio_buffer_underruns": 0,
            
            # 吞吐量指标
            "questions_per_minute": deque(maxlen=60),
            "audio_chunks_per_second": deque(maxlen=60),
            
            # 资源使用指标
            "memory_usage_samples": deque(maxlen=120),
            "cpu_usage_samples": deque(maxlen=120),
            "connection_count_samples": deque(maxlen=120)
        }
        
    def record_tts_latency(self, latency_ms: float):
        """记录TTS延迟"""
        self.metrics["tts_latency_ms"].append(latency_ms)
        
    def record_qa_response_time(self, response_time_ms: float):
        """记录QA响应时间"""
        self.metrics["qa_response_time_ms"].append(response_time_ms)
        
    def get_performance_summary(self) -> dict:
        """获取性能摘要"""
        tts_latencies = list(self.metrics["tts_latency_ms"])
        qa_times = list(self.metrics["qa_response_time_ms"])
        
        return {
            "tts_performance": {
                "avg_latency_ms": sum(tts_latencies) / len(tts_latencies) if tts_latencies else 0,
                "p95_latency_ms": self._percentile(tts_latencies, 0.95),
                "max_latency_ms": max(tts_latencies) if tts_latencies else 0
            },
            "qa_performance": {
                "avg_response_time_ms": sum(qa_times) / len(qa_times) if qa_times else 0,
                "p95_response_time_ms": self._percentile(qa_times, 0.95),
                "max_response_time_ms": max(qa_times) if qa_times else 0
            },
            "reliability": {
                "buffer_underrun_count": self.metrics["audio_buffer_underruns"],
                "error_rate_percent": self._calculate_error_rate()
            }
        }
        
    def _percentile(self, data: list, percentile: float) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile)
        return sorted_data[min(index, len(sorted_data) - 1)]
```

## 12. 扩展性和通用中断机制设计

### 12.1. 插件式扩展架构

#### 12.1.1. 处理器接口设计
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class InterruptHandler(ABC):
    """中断处理器抽象基类"""
    
    @abstractmethod
    async def can_interrupt(self, context: Dict[str, Any]) -> bool:
        """判断是否可以中断"""
        pass
    
    @abstractmethod
    async def handle_interrupt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理中断事件"""
        pass
    
    @abstractmethod
    async def restore_context(self, context: Dict[str, Any]) -> None:
        """恢复中断前的上下文"""
        pass
    
    @property
    @abstractmethod
    def priority(self) -> int:
        """处理器优先级（数字越小优先级越高）"""
        pass
```

#### 12.1.2. 中断管理器
```python
class InterruptManager:
    """通用中断管理器"""
    
    def __init__(self):
        self._handlers: Dict[str, InterruptHandler] = {}
        self._interrupt_queue = asyncio.Queue()
        self._is_processing = False
        self._context_stack = []  # 中断上下文栈
        
    def register_handler(self, event_type: str, handler: InterruptHandler):
        """注册中断处理器"""
        self._handlers[event_type] = handler
        logger.info(f"注册中断处理器: {event_type} (优先级: {handler.priority})")
        
    async def request_interrupt(self, event_type: str, data: Dict[str, Any]) -> bool:
        """请求中断处理"""
        if event_type not in self._handlers:
            logger.warning(f"未找到处理器: {event_type}")
            return False
            
        # 检查是否可以中断
        handler = self._handlers[event_type]
        context = self._get_current_context()
        
        if not await handler.can_interrupt(context):
            logger.info(f"当前不能处理中断: {event_type}")
            return False
            
        # 加入中断队列
        await self._interrupt_queue.put({
            "event_type": event_type,
            "handler": handler,
            "data": data,
            "context": context,
            "timestamp": asyncio.get_event_loop().time()
        })
        
        return True
        
    async def process_interrupts(self):
        """处理中断队列"""
        while True:
            try:
                interrupt_request = await self._interrupt_queue.get()
                await self._handle_single_interrupt(interrupt_request)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"中断处理异常: {e}")
                
    async def _handle_single_interrupt(self, request: Dict[str, Any]):
        """处理单个中断请求"""
        handler = request["handler"]
        event_type = request["event_type"]
        
        try:
            self._is_processing = True
            
            # 保存当前上下文
            self._context_stack.append(request["context"])
            
            # 执行中断处理
            result = await handler.handle_interrupt(request["data"])
            
            logger.info(f"中断处理完成: {event_type}")
            
            # 恢复上下文
            if self._context_stack:
                context = self._context_stack.pop()
                await handler.restore_context(context)
                
        except Exception as e:
            logger.error(f"中断处理失败: {event_type}, 错误: {e}")
        finally:
            self._is_processing = False
```

### 12.2. 具体处理器实现

#### 12.2.1. QA 中断处理器
```python
class QAInterruptHandler(InterruptHandler):
    """QA 中断处理器"""
    
    def __init__(self, player: MainContentPlayer, qa_manager: QAManager):
        self.player = player
        self.qa_manager = qa_manager
        self._priority = 1  # 高优先级
        
    @property
    def priority(self) -> int:
        return self._priority
        
    async def can_interrupt(self, context: Dict[str, Any]) -> bool:
        """检查是否可以进行QA中断"""
        # 检查播放器是否在安全间隙
        player_status = self.player.get_status()
        
        # 如果正在播放句子，不能中断
        if player_status.get("is_speaking", False):
            return False
            
        # 如果在句子间隙，可以中断
        if player_status.get("is_at_sentence_gap", False):
            return True
            
        # 如果没有在播放，可以中断
        return not player_status.get("is_playing", False)
        
    async def handle_interrupt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理QA中断"""
        question = data.get("question", "")
        
        # 暂停播放
        pause_result = await self.player.pause()
        if not pause_result["success"]:
            raise Exception(f"暂停播放失败: {pause_result['error']}")
            
        # 处理QA
        qa_result = await self.qa_manager.handle_qa(question)
        
        return {
            "qa_result": qa_result,
            "pause_result": pause_result
        }
        
    async def restore_context(self, context: Dict[str, Any]) -> None:
        """恢复播放上下文"""
        # 恢复播放
        resume_result = await self.player.resume()
        if not resume_result["success"]:
            logger.error(f"恢复播放失败: {resume_result['error']}")
```

#### 12.2.2. 紧急停止处理器
```python
class EmergencyStopHandler(InterruptHandler):
    """紧急停止处理器"""
    
    def __init__(self, controller: 'LiveStreamController'):
        self.controller = controller
        self._priority = 0  # 最高优先级
        
    @property
    def priority(self) -> int:
        return self._priority
        
    async def can_interrupt(self, context: Dict[str, Any]) -> bool:
        """紧急停止总是可以中断"""
        return True
        
    async def handle_interrupt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理紧急停止"""
        reason = data.get("reason", "未知原因")
        
        logger.warning(f"触发紧急停止: {reason}")
        
        # 立即停止所有活动
        await self.controller.stop_all()
        
        return {
            "stopped": True,
            "reason": reason,
            "timestamp": asyncio.get_event_loop().time()
        }
        
    async def restore_context(self, context: Dict[str, Any]) -> None:
        """紧急停止后不恢复上下文"""
        pass
```

### 12.3. 扩展机制集成

#### 12.3.1. 修改 LiveStreamController
```python
class LiveStreamController:
    def __init__(self):
        # 原有组件
        self.player = MainContentPlayer()
        self.qa_manager = QAManager()
        
        # 新增：中断管理器
        self.interrupt_manager = InterruptManager()
        self._setup_interrupt_handlers()
        
    def _setup_interrupt_handlers(self):
        """设置中断处理器"""
        # 注册QA处理器
        qa_handler = QAInterruptHandler(self.player, self.qa_manager)
        self.interrupt_manager.register_handler("qa_request", qa_handler)
        
        # 注册紧急停止处理器
        emergency_handler = EmergencyStopHandler(self)
        self.interrupt_manager.register_handler("emergency_stop", emergency_handler)
        
    async def start(self, script_content: List[str]):
        """启动直播流"""
        # 启动中断处理任务
        interrupt_task = asyncio.create_task(
            self.interrupt_manager.process_interrupts()
        )
        self.task_manager.register_task("interrupt_processor", interrupt_task)
        
        # 其他启动逻辑...
        
    async def handle_qa_request(self, question: str) -> bool:
        """处理QA请求"""
        return await self.interrupt_manager.request_interrupt(
            "qa_request", 
            {"question": question}
        )
        
    async def emergency_stop(self, reason: str = "用户请求"):
        """紧急停止"""
        return await self.interrupt_manager.request_interrupt(
            "emergency_stop",
            {"reason": reason}
        )
```

#### 12.3.2. 第三方扩展示例
```python
class CustomNotificationHandler(InterruptHandler):
    """自定义通知处理器示例"""
    
    def __init__(self, notification_service):
        self.notification_service = notification_service
        self._priority = 5  # 低优先级
        
    @property 
    def priority(self) -> int:
        return self._priority
        
    async def can_interrupt(self, context: Dict[str, Any]) -> bool:
        """只在播放间隙处理通知"""
        return not context.get("is_critical_moment", False)
        
    async def handle_interrupt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送通知"""
        message = data.get("message", "")
        await self.notification_service.send_notification(message)
        return {"notification_sent": True}
        
    async def restore_context(self, context: Dict[str, Any]) -> None:
        """通知不需要恢复上下文"""
        pass

# 使用方式
controller.interrupt_manager.register_handler(
    "custom_notification", 
    CustomNotificationHandler(notification_service)
)
```

## 13. 细化测试策略

### 13.1. 测试层次结构

#### 13.1.1. 单元测试（Unit Tests）
```python
# tests/unit/test_interrupt_manager.py
@pytest.mark.asyncio
async def test_interrupt_manager_priority_handling():
    """测试中断管理器的优先级处理"""
    manager = InterruptManager()
    
    # 创建不同优先级的处理器
    high_priority = MockHandler(priority=1)
    low_priority = MockHandler(priority=5)
    
    manager.register_handler("high", high_priority)
    manager.register_handler("low", low_priority)
    
    # 同时提交两个中断请求
    await manager.request_interrupt("low", {})
    await manager.request_interrupt("high", {})
    
    # 验证高优先级先处理
    # 测试实现...

@pytest.mark.asyncio  
async def test_player_state_transitions():
    """测试播放器状态转换"""
    player = MainContentPlayer()
    
    # 测试正常播放流程
    await player.play(["句子1", "句子2"])
    assert player.get_status()["is_playing"] is True
    
    # 测试暂停/恢复
    await player.pause()
    assert player.get_status()["is_paused"] is True
    
    await player.resume()
    assert player.get_status()["is_paused"] is False
```

#### 13.1.2. 组件测试（Component Tests）
```python
# tests/component/test_qa_flow.py
@pytest.mark.asyncio
async def test_complete_qa_flow():
    """测试完整的QA处理流程"""
    controller = LiveStreamController()
    
    # 启动播放
    script = ["这是第一句话", "这是第二句话"] 
    await controller.start(script)
    
    # 等待播放开始
    await asyncio.sleep(0.5)
    
    # 提交QA请求
    qa_success = await controller.handle_qa_request("什么是AI?")
    assert qa_success is True
    
    # 验证播放暂停
    status = controller.player.get_status()
    assert status["is_paused"] is True
    
    # 等待QA处理完成
    await asyncio.sleep(2)
    
    # 验证播放恢复
    final_status = controller.player.get_status()
    assert final_status["is_paused"] is False

@pytest.mark.asyncio
async def test_concurrent_qa_requests():
    """测试并发QA请求的处理"""
    controller = LiveStreamController()
    await controller.start(["测试脚本内容"])
    
    # 同时提交多个QA请求
    tasks = [
        controller.handle_qa_request(f"问题{i}") 
        for i in range(5)
    ]
    
    results = await asyncio.gather(*tasks)
    
    # 验证只有第一个请求成功
    assert sum(results) == 1
```

#### 13.1.3. 集成测试（Integration Tests）
```python
# tests/integration/test_full_streaming_session.py
@pytest.mark.asyncio
async def test_full_streaming_session():
    """测试完整的直播会话"""
    # 模拟完整的直播场景
    controller = LiveStreamController()
    
    script_content = [
        "欢迎来到今天的直播",
        "今天我们要讨论人工智能的发展",
        "首先让我们看看机器学习的基础概念"
    ]
    
    # 启动直播
    await controller.start(script_content)
    
    # 模拟用户互动
    qa_requests = [
        "什么是机器学习?",
        "AI的未来发展如何?",
        "深度学习和机器学习有什么区别?"
    ]
    
    # 在播放过程中插入QA
    for i, question in enumerate(qa_requests):
        await asyncio.sleep(1)  # 等待一段时间
        success = await controller.handle_qa_request(question)
        assert success, f"QA请求 {i+1} 失败"
    
    # 等待播放完成
    await controller.wait_for_completion(timeout=60)
    
    # 验证最终状态
    final_status = controller.get_status()
    assert final_status["is_completed"] is True
    assert final_status["total_qa_handled"] == len(qa_requests)
```

### 13.2. 性能测试

#### 13.2.1. 压力测试
```python
# tests/performance/test_load.py
@pytest.mark.asyncio
async def test_high_frequency_qa_requests():
    """测试高频QA请求处理能力"""
    controller = LiveStreamController()
    await controller.start(["长时间播放的测试内容"] * 100)
    
    # 高频提交QA请求
    start_time = time.time()
    successful_requests = 0
    
    for i in range(100):
        success = await controller.handle_qa_request(f"测试问题{i}")
        if success:
            successful_requests += 1
        await asyncio.sleep(0.1)  # 100ms间隔
    
    end_time = time.time()
    
    # 验证性能指标
    assert successful_requests > 0
    avg_response_time = (end_time - start_time) / successful_requests
    assert avg_response_time < 0.5  # 平均响应时间小于500ms

@pytest.mark.asyncio
async def test_memory_usage_stability():
    """测试长时间运行的内存稳定性"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss
    
    controller = LiveStreamController()
    
    # 运行多个会话周期
    for session in range(10):
        script = [f"会话{session}的测试内容"] * 50
        await controller.start(script)
        
        # 处理一些QA
        for qa in range(10):
            await controller.handle_qa_request(f"会话{session}-问题{qa}")
            await asyncio.sleep(0.1)
        
        await controller.stop()
        await controller.cleanup()
        
        # 检查内存使用
        current_memory = process.memory_info().rss
        memory_growth = (current_memory - initial_memory) / initial_memory
        
        # 内存增长不应超过50%
        assert memory_growth < 0.5, f"内存泄漏检测：增长{memory_growth:.2%}"
```

### 13.3. 错误场景测试

#### 13.3.1. 网络故障模拟
```python
# tests/error_scenarios/test_network_failures.py
@pytest.mark.asyncio
async def test_tts_connection_failure():
    """测试TTS连接失败的处理"""
    controller = LiveStreamController()
    
    # 模拟TTS服务不可用
    with patch.object(controller.player.tts_engine, 'synthesize') as mock_tts:
        mock_tts.side_effect = ConnectionError("TTS服务连接失败")
        
        # 启动播放应该失败并触发错误恢复
        with pytest.raises(TTSConnectionError):
            await controller.start(["测试内容"])
            
        # 验证错误恢复机制
        assert controller.error_recovery.is_active
        
@pytest.mark.asyncio
async def test_partial_audio_generation_failure():
    """测试部分音频生成失败的处理"""
    controller = LiveStreamController()
    
    script = ["句子1", "句子2", "句子3", "句子4", "句子5"]
    
    # 模拟第3句音频生成失败
    with patch.object(controller.player.tts_engine, 'synthesize') as mock_tts:
        def side_effect(text):
            if "句子3" in text:
                raise TTSError("音频生成失败")
            return b"mock_audio_data"
            
        mock_tts.side_effect = side_effect
        
        await controller.start(script)
        
        # 验证其他句子正常播放
        final_status = controller.get_status()
        assert final_status["sentences_played"] == 4  # 跳过失败的句子
        assert final_status["errors_encountered"] == 1
```

### 13.4. 自动化测试集成

#### 13.4.1. GitHub Actions 配置
```yaml
# .github/workflows/test.yml
name: Comprehensive Testing

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-asyncio pytest-cov
      
      - name: Run unit tests
        run: |
          pytest tests/unit/ -v --cov=src/ai_live_streamer --cov-report=xml
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Run integration tests
        run: |
          pytest tests/integration/ -v --timeout=300
          
  performance-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Run performance tests
        run: |
          pytest tests/performance/ -v --benchmark-only
```

#### 13.4.2. 测试覆盖率要求
```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --disable-warnings
    --cov=src/ai_live_streamer
    --cov-report=term-missing
    --cov-report=html
    --cov-fail-under=85
asyncio_mode = auto

markers =
    unit: 单元测试
    component: 组件测试  
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试
```

通过这个细化的测试策略，我们可以确保新架构的可靠性和性能表现，同时为后续的维护和扩展提供坚实的质量保障基础。

## 14. 架构扩展性：支持动态内容生成

本架构的核心优势之一是其对动态内容源的天然支持。通过将脚本源从静态的 `List` 抽象为 `AsyncGenerator`，我们可以实现直播过程中脚本的动态、无限扩展。

### 14.1. 实现模式：异步生产者-消费者

我们采用经典的**异步生产者-消费者模式**：

- **消费者 (`MainContentPlayer`)**: 其 `play` 方法接收一个 `AsyncGenerator` 作为输入，通过 `async for` 循环消费句子。
- **生产者 (`script_generator`)**: 一个异步生成器函数，负责在内部维护句子库存。当库存低于阈值时，它会在后台异步调用 LLM 服务生成新内容，并 `yield` 给消费者。

#### 14.1.1. 更新的播放器接口

```python
from typing import AsyncGenerator, List, Union

class IMainContentPlayer(ABC):
    """播放器接口 - 支持动态内容源"""
    
    @abstractmethod
    async def play(self, 
                   sentence_provider: Union[List[str], AsyncGenerator[str, None]]) -> None:
        """
        播放内容
        
        Args:
            sentence_provider: 句子来源，可以是：
                - List[str]: 静态的句子列表
                - AsyncGenerator[str, None]: 动态的句子生成器
        """
        pass

class MainContentPlayer(IMainContentPlayer):
    """主内容播放器实现"""
    
    async def play(self, 
                   sentence_provider: Union[List[str], AsyncGenerator[str, None]]) -> None:
        """播放内容 - 自适应静态/动态内容源"""
        
        # 统一处理接口：将列表转换为异步生成器
        if isinstance(sentence_provider, list):
            async def _list_generator():
                for sentence in sentence_provider:
                    yield sentence
            sentence_provider = _list_generator()
        
        # 核心播放循环 - 使用 async for
        try:
            async for sentence in sentence_provider:
                # 检查暂停状态
                await self._pause_event.wait()
                
                # 检查停止信号
                if self._stop_requested:
                    break
                
                # 生成并播放音频
                audio_data = await self.tts_engine.synthesize(sentence)
                await self._play_audio(audio_data)
                
                # 更新状态
                self.current_sentence_index += 1
                
        except Exception as e:
            logger.error(f"播放错误: {e}")
            raise
```

### 14.2. 动态脚本生成器实现

#### 14.2.1. 基础脚本生成器

```python
class ScriptGenerator:
    """动态脚本生成器"""
    
    def __init__(self, llm_service: LLMService):
        self.llm_service = llm_service
        self.min_buffer_size = 5  # 最小缓冲句子数
        self.generation_batch_size = 10  # 每次生成的句子数
        
    async def generate_script_stream(self, 
                                   initial_content: str,
                                   context_window: int = 3) -> AsyncGenerator[str, None]:
        """
        生成无限脚本流
        
        Args:
            initial_content: 初始脚本内容
            context_window: 用于生成的上下文窗口大小
            
        Yields:
            str: 下一个要播放的句子
        """
        # 1. 处理初始内容
        sentences = self._split_into_sentences(initial_content)
        buffer = deque(sentences)
        context = deque(maxlen=context_window)
        
        # 2. 启动后台生成任务
        generation_task = None
        pending_generation = asyncio.Queue()
        
        try:
            while True:
                # 3. 从缓冲区提供句子
                if buffer:
                    sentence = buffer.popleft()
                    context.append(sentence)
                    yield sentence
                    
                    # 4. 检查是否需要补充缓冲区
                    if len(buffer) < self.min_buffer_size and generation_task is None:
                        # 启动异步生成
                        generation_task = asyncio.create_task(
                            self._generate_more_content(
                                context=list(context),
                                target_queue=pending_generation
                            )
                        )
                
                # 5. 检查是否有新生成的内容
                elif not pending_generation.empty():
                    # 从生成队列获取新内容
                    try:
                        new_sentences = pending_generation.get_nowait()
                        buffer.extend(new_sentences)
                        generation_task = None
                    except asyncio.QueueEmpty:
                        pass
                        
                # 6. 等待新内容生成
                else:
                    if generation_task and not generation_task.done():
                        # 等待生成完成
                        await asyncio.sleep(0.1)
                    else:
                        # 无法生成更多内容，结束流
                        logger.info("脚本生成已结束")
                        break
                        
        finally:
            # 清理任务
            if generation_task and not generation_task.done():
                generation_task.cancel()
                
    async def _generate_more_content(self, 
                                   context: List[str], 
                                   target_queue: asyncio.Queue) -> None:
        """后台内容生成任务"""
        try:
            # 构建提示词
            context_text = " ".join(context)
            prompt = f"""
            基于以下上下文，继续生成直播脚本内容。
            保持风格和主题的一致性。
            
            上下文：
            {context_text}
            
            请生成接下来的 {self.generation_batch_size} 句话：
            """
            
            # 调用 LLM
            response = await self.llm_service.generate(
                prompt=prompt,
                max_tokens=500,
                temperature=0.7
            )
            
            # 解析生成的内容
            new_sentences = self._split_into_sentences(response)
            
            # 放入队列
            await target_queue.put(new_sentences)
            
        except Exception as e:
            logger.error(f"内容生成失败: {e}")
            # 即使失败也要放入空列表，避免死锁
            await target_queue.put([])
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """将文本分割为句子"""
        # 使用 NLP 库进行智能句子分割
        import spacy
        nlp = spacy.load("zh_core_web_sm")  # 或 en_core_web_sm
        doc = nlp(text)
        return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
```

#### 14.2.2. 智能预加载策略

```python
class SmartScriptGenerator(ScriptGenerator):
    """带智能预加载的脚本生成器"""
    
    def __init__(self, llm_service: LLMService):
        super().__init__(llm_service)
        self.generation_history = []
        self.avg_sentence_duration = 3.0  # 平均句子播放时长（秒）
        
    async def generate_script_stream_with_preload(self, 
                                                 initial_content: str) -> AsyncGenerator[str, None]:
        """带预测性预加载的脚本流生成"""
        
        sentences = self._split_into_sentences(initial_content)
        buffer = deque(sentences)
        
        # 预加载协程
        preload_task = None
        preload_threshold = self._calculate_preload_threshold()
        
        async for sentence in self._yield_with_timing(buffer):
            yield sentence
            
            # 动态调整预加载阈值
            remaining_buffer_time = len(buffer) * self.avg_sentence_duration
            
            if remaining_buffer_time < preload_threshold and preload_task is None:
                # 提前触发预加载
                logger.info(f"触发预加载，剩余缓冲时间: {remaining_buffer_time:.1f}秒")
                preload_task = asyncio.create_task(
                    self._preload_content(buffer)
                )
                
    def _calculate_preload_threshold(self) -> float:
        """计算预加载阈值（秒）"""
        # 基于历史生成时间和网络延迟计算
        if self.generation_history:
            avg_generation_time = sum(self.generation_history) / len(self.generation_history)
            # 预加载阈值 = 平均生成时间 + 安全余量
            return avg_generation_time + 5.0
        else:
            # 默认阈值
            return 15.0
```

### 14.3. 控制器集成

#### 14.3.1. 更新的控制器实现

```python
class LiveStreamController:
    """支持动态内容的控制器"""
    
    def __init__(self,
                 player: IMainContentPlayer,
                 qa_manager: IQAManager,
                 session: LiveStreamSession,
                 script_generator: Optional[ScriptGenerator] = None):
        self.player = player
        self.qa_manager = qa_manager
        self.session = session
        self.script_generator = script_generator
        
        # 任务管理
        self.play_task: Optional[asyncio.Task] = None
        self.qa_monitor_task: Optional[asyncio.Task] = None
        
    async def start(self, 
                   script_content: Union[str, List[str]],
                   dynamic_mode: bool = False):
        """
        启动直播流
        
        Args:
            script_content: 初始脚本内容
            dynamic_mode: 是否启用动态生成模式
        """
        try:
            # 准备内容源
            if dynamic_mode and self.script_generator:
                # 动态模式：使用脚本生成器
                initial_text = script_content if isinstance(script_content, str) else " ".join(script_content)
                sentence_provider = self.script_generator.generate_script_stream(initial_text)
            else:
                # 静态模式：使用列表
                sentence_provider = script_content if isinstance(script_content, list) else [script_content]
            
            # 启动播放任务
            self.play_task = asyncio.create_task(
                self.player.play(sentence_provider)
            )
            
            # 启动 QA 监听任务
            self.qa_monitor_task = asyncio.create_task(
                self.monitor_questions()
            )
            
            logger.info(f"直播已启动，模式: {'动态' if dynamic_mode else '静态'}")
            
        except Exception as e:
            logger.error(f"启动失败: {e}")
            await self.stop()
            raise
```

### 14.4. API 层支持

```python
@app.post("/start_stream")
async def start_stream(
    request: StreamRequest,
    player: IMainContentPlayer = Depends(get_player),
    qa_manager: IQAManager = Depends(get_qa_manager),
    llm_service: LLMService = Depends(get_llm_service)
):
    """启动直播流 - 支持静态和动态模式"""
    
    # 创建会话
    session = LiveStreamSession(session_id=str(uuid.uuid4()))
    
    # 根据请求决定是否使用动态生成
    script_generator = None
    if request.enable_dynamic_generation:
        script_generator = ScriptGenerator(llm_service)
    
    # 创建控制器
    controller = LiveStreamController(
        player=player,
        qa_manager=qa_manager,
        session=session,
        script_generator=script_generator
    )
    
    # 存储会话
    active_sessions[session.session_id] = controller
    
    # 启动直播
    await controller.start(
        script_content=request.script_content,
        dynamic_mode=request.enable_dynamic_generation
    )
    
    return {
        "session_id": session.session_id,
        "mode": "dynamic" if request.enable_dynamic_generation else "static"
    }
```

### 14.5. 优势总结

1. **非阻塞生成**: LLM 的调用是异步的，不会阻塞正在进行的音频播放。
2. **无限流**: 理论上可以支持无限时长的直播，脚本可以根据需要动态生成。
3. **上下文连续性**: 生产者在生成新内容时，将最后几句已播放的句子作为上下文传递给 LLM，保证故事的连贯性。
4. **向后兼容**: 通过统一的接口设计，既支持传统的静态脚本列表，也支持动态生成器。
5. **不影响核心逻辑**: 这种扩展**无需修改** `LiveStreamController` 的核心协调逻辑（如 QA 中断处理），只需改变它传递给 `Player` 的数据源。
6. **智能预加载**: 根据播放速度和生成延迟，智能地提前触发内容生成，确保播放的流畅性。

这种设计充分体现了简单并发架构的灵活性和可扩展性，为未来的功能增强提供了坚实的基础。

