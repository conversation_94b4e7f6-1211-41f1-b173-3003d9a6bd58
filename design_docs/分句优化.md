## 统一分句方案总结与落地计划（不修改现有代码）

### 背景与目标
- 背景：当前系统在多个位置执行文本分句/切分（TTSManager、句级播放器、CosyVoice 流式、语义拆分器），整体方向为“按强标点分句、不按逗号硬切”，但缺少“短句合并”的硬约束，仍会产生过短片段，影响语感与节奏。
- 目标：在不修改现有代码的前提下，给出统一分句的最佳实践方案与落地计划，使后续改造能一次性达成：
  - 短句自动合并（满足最小播读单元阈值）
  - 超长片段再切（优先用软标点作为回切点）
  - 保持 DRY 原则，统一分句逻辑一处实现
  - 出错就报错，不添加隐藏 fallback

---

## 现状分析（四个关键位置）

### 1) TTSManager._split_text：性能导向的块切分（非语义分句）
- 作用：按词数固定切分，侧重批量吞吐优化；中文场景下由于没有空格，几乎不生效（大概率返回原文）
- 风险：分句职责落在其它模块，容易逻辑分散与不一致

````python path=src/ai_live_streamer/services/tts_manager.py mode=EXCERPT
def _split_text(self, text: str) -> List[str]:
    chunk_size = cfg.get_yaml_config('performance.tts.chunk_size_words', TTS_CHUNK_SIZE_WORDS)
    words = text.split()
    chunks = []
    for i in range(0, len(words), chunk_size):
        chunks.append(' '.join(words[i:i + chunk_size]))
    return chunks if chunks else [text]
````

### 2) 句级播放器 sentence_level_tts：强标点分句 + 过滤极短句
- 作用：按 [.!?。！？；;] 分句，不过滤逗号；仅剔除极短句（<4字符），未与相邻句合并
- 风险：短句（语气词、引导语、括注等）仍可能保留，语感破碎

````python path=src/ai_live_streamer/services/sentence_level_tts.py mode=EXCERPT
sentence_pattern = r'[.!?。！？；;]+\s*'
sentences = re.split(sentence_pattern, text.strip())
if len(sentence) > 3:
    clean_sentences.append(sentence)
````

### 3) CosyVoice v2 流式分段：强标点分段 + 补句号
- 作用：按句号类切分并补标点；不按逗号分句
- 风险：不做短句合并，流式场景可能更频繁出现短片段

````python path=src/ai_live_streamer/services/cosyvoice_v2_tts.py mode=EXCERPT
segments = re.split(r'[。！？.!?]', text)
for i, segment in enumerate(segments):
    segment = segment.strip()
    if segment and i < len(segments) - 1:
        segment += '。'
    result.append(segment)
````

说明：当前系统使用的是统一引擎 cosyvoice_unified_engine，cosyvoice_v2_tts 为遗留/冗余实现，不在生产路径；但其“内部分段”更体现了“不要把分句耦合到引擎”的反面案例。

### 4) 语义拆分器 SemanticSentenceSplitter：具备“合并 + 长句再切”的雏形
- 流程：spaCy 初分句 → 语义优化（可合并）→ 长句再切 → 质量验证
- 优势：可以承载“短句合并 + 稳健切分”的统一逻辑核心

````python path=src/ai_live_streamer/core/semantic_sentence_splitter.py mode=EXCERPT
def split_sentences_semantic(self, content: str) -> List[SemanticSentence]:
    spacy_sentences = self._extract_spacy_sentences(content)
    optimized_sentences = self._optimize_sentence_boundaries(spacy_sentences)
    semantic_sentences = self._build_semantic_sentences(optimized_sentences)
    self._validate_semantic_sentences(semantic_sentences)
    return semantic_sentences
````

---

## 统一分句的最佳实践

- 分界符层级
  - 强标点（。！？；.!?;）= 默认边界
  - 软标点（，、：,:—…括号/引号等）= 不做硬切，仅用于“超长回切”的候选切点
- 最小播读单元
  - 字符阈值（中文）建议 ≥ 12–15
  - 或时长阈值 ≥ 700–800ms（估算每字 ~60ms）
  - 低于下限的片段应与相邻片段合并
- 最大限制与回切
  - 最大字符/时长上限（如 70–80 字，5–6 秒）
  - 超出时优先在软标点回切，再退化到分词/NER 辅助切点
- 语义优先
  - 合并尽量向后合并，保持自然语流
  - 保护实体/数字+单位/时间短语，不在中间切割
- 流式低延迟
  - Lookahead 小窗口（150–250ms）：遇短句先短暂持有，若后句到达且合并后更自然则合并，否则发出
- 错误处理
  - 统一 Fail-Fast：出错就报错，不添加隐性 fallback

---

## 统一方案设计（不改代码的应用思路）

将 core/semantic_sentence_splitter.py 包装为“统一句源服务（SentenceSegmentationService）”，作为系统唯一的分句与短句合并入口；各消费方不再自持分句规则。

统一服务（未来实现接口示例，当前仅设计）：
- segment_for_playback(text, policy) -> List[Sentence]
- segment_for_streaming(text, policy) -> Async/List[Sentence]
- segment_for_manager(text, policy) -> List[Sentence]

Policy（配置化）：
- min_chars / min_ms
- max_chars / max_ms
- estimate_ms_per_char（默认60）
- allow_soft_punct_cut_when_overflow（True）
- streaming_lookahead_ms（150–250ms）

---

## 各模块应用方案（不修改代码，给出对接设计）

### A. TTSManager._split_text（管理层吞吐优化）
- 现状：基于词数的切分对中文不适用
- 设计：改为“使用统一句源”的输出作为基础单元；仅在需要满足引擎吞吐/缓存粒度时，做二次聚合（不破坏语义句边界）
- 效果：TTSManager 不再承担“按词数分句”，而是消费统一句源的“播读友好片段”，确保上层一致性

参考位置（仅说明用途）：
````python path=src/ai_live_streamer/services/tts_manager.py mode=EXCERPT
# 未来将 _split_text 切换为调用 segment_for_manager(text, policy)
# 返回的 chunks 为已合并短句/限长后的片段
````

### B. sentence_level_tts（句级播放器）
- 现状：强标点正则 + 极短句过滤
- 设计：替换为“调用统一句源 segment_for_playback”，句级播放器只负责播放与中断检查，不再持有分句规则
- 效果：短句得到合并；播放节奏自然，便于中断点落在合适边界

参考位置：
````python path=src/ai_live_streamer/services/sentence_level_tts.py mode=EXCERPT
# 未来将 _split_text_into_sentences 改为调用 segment_for_playback
# self.current_sentences = map(统一句源结果 → SentenceInfo)
````

### C. CosyVoice 流式（统一引擎使用者）
- 当前在生产路径使用 cosyvoice_unified_engine（不做分句，架构正确）
- 对照冗余的 cosyvoice_v2_tts（内部分段）：说明未来不应在引擎内分段
- 设计：流式上层调用 segment_for_streaming（带 lookahead 窗口），统一句源负责“短句合并+低延迟”，引擎仍只负责合成
- 效果：自然的片段粒度、低延迟与统一节奏管理

参考位置（对冗余文件的改造设想，仅说明）：
````python path=src/ai_live_streamer/services/cosyvoice_v2_tts.py mode=EXCERPT
# 未来如需恢复使用，应将 _split_text_for_streaming 替换为 segment_for_streaming
# 但当前生产不使用本文件，无需处理
````

### D. SemanticSentenceSplitter（统一句源的核心）
- 现状：已具备“合并 + 再切”雏形；需要显式引入最小/最大阈值的硬约束与 lookahead 参数
- 设计：在 splitter 内部完善 Policy 接口，实现统一的“短句合并/限长回切/软标点回切/实体保护/估时”等

参考位置：
````python path=src/ai_live_streamer/core/semantic_sentence_splitter.py mode=EXCERPT
# 未来在 _optimize_sentence_boundaries / _split_long_sentence_semantically 中
# 引入 min/max 阈值与软标点回切策略
````

---

## 配置建议（为后续接入预先约定）

- features.tts.intelligent_sentence_merge: true/false
- tts.sentence_merge:
  - min_chars: 12–15
  - min_ms: 700–800
  - max_chars: 70–80
  - max_ms: 5000–6000
  - estimate_ms_per_char: 60
  - allow_soft_punct_cut_when_overflow: true
  - streaming_lookahead_ms: 150–250

说明：
- 配置集中在一个模块被统一句源消费，保持 DRY，不将阈值散落在 TTSManager/播放器/引擎中。

---

## 实施计划（最佳时间安排，不动代码）

- 阶段 0（0.5 天）：对齐参数与场景（中文为主、少量英文）
- 阶段 1（1–2 天）：离线原型（独立脚本）实现“短句合并+限长回切+软标点回切+实体保护”；准备 200–300 段样本与统计脚本（短句占比/超长占比/分布）
- 阶段 2（2 天）：合成小样 AB 听测（节奏/停顿/自然度），校准参数
- 阶段 3（1 天）：流式策略评估（lookahead 150–250ms 对延迟与语感的影响）；目标延迟增加 < +100ms
- 阶段 4（0.5 天）：统一设计定稿（接口/配置/错误处理），标注 TTSManager、sentence_level_tts、（如需）cosyvoice_v2_tts 的对接方式
- 阶段 5（1 天）：上线计划书（灰度策略/监控指标/回滚方式：配置关闭统一句源；不提供代码层 fallback）

---

## 验收指标与评估

- 短句占比（< min_chars）：显著下降
- 超长片段占比：稳定低位
- 听感评分（节奏/停顿/自然度）：AB 测试提升
- 延迟：端到端 P50/P95 无显著恶化（流式 < +100ms）
- 中断点精度：QA/打断落点更贴近自然边界

---

## 风险与注意事项

- 严格 DRY：统一分句在“统一句源”中实现，一处维护，三处消费
- 不要按逗号硬分句：逗号仅在“超长回切”作为候选切点
- 出错就报错：遵循“不要添加 fallback 机制，出错就报错”，统一句源异常直接抛错，便于定位
- 与缓存/吞吐互动：片段粒度变化会影响缓存命中率与合成吞吐；评估阶段需同步观察

---

## 结论

- 现状分句方向正确，但缺少“短句合并”的硬约束，导致偶发短句、语感破碎。
- 以 SemanticSentenceSplitter 为核心，构建“统一句源服务”，可在不改动引擎职责的前提下，向 TTSManager、句级播放器、流式调用统一供给“播读友好”的片段，满足 DRY 与 Fail-Fast 要求。
- 本文档给出了清晰的设计与计划，后续仅需按计划完成离线原型与评估，再在三个消费点以最小改动接入，即可实现统一分句与短句合并的目标。
