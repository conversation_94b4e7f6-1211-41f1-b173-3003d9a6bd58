"""LiveStream state management and pace configuration models

⚠️ LEGACY ARCHITECTURE WARNING ⚠️

此文件包含LangGraph状态机时代的复杂状态管理逻辑。
新架构采用session-based管理，计划逐步迁移到更简化的模型。

架构迁移进度：
- [x] LiveStreamSession 已实现asyncio.Queue管理 (core/live_stream_session.py)  
- [x] PriorityAudioManager 已移除全局状态依赖 (services/priority_audio_manager.py)
- [ ] StreamConfigurationExtractor 待迁移 (services/stream_configuration_extractor.py)
- [ ] 前端API适配器 待实现 (api/control.py)
- [ ] 复杂状态字段清理 待完成

新架构原则：
1. 无全局状态 - 使用session-based局部状态
2. 异步队列 - asyncio.Queue替代状态机队列
3. 简化配置 - 最小必要字段集合

Central state definition for LangGraph state machine with 13 essential fields
across 4 categories: Content, Interaction, Context, and Control.
"""

from datetime import datetime
from enum import Enum
from typing import Annotated, Any, Optional, List, Dict

from pydantic import BaseModel, Field

from .constants import PACE_MAPPING


class StreamStatus(Enum):
    """Live stream status enumeration"""
    INITIALIZING = "initializing"
    PLANNING = "planning"
    NARRATING = "narrating"
    QA_MODE = "qa_mode"
    TRANSITIONING = "transitioning"
    PAUSED = "paused"
    DEGRADED = "degraded"
    STOPPED = "stopped"


class PaceLevel(Enum):
    """Pace level based on viewer count"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class PaceConfig(BaseModel):
    """Dynamic pace configuration based on viewer count"""

    level: PaceLevel
    words_per_minute: int = Field(ge=100, le=300)
    pause_duration_ms: int = Field(ge=0, le=2000)
    segment_length_words: int = Field(ge=10, le=100)
    qa_frequency_ratio: float = Field(ge=0.0, le=1.0)
    viewer_count: int = Field(ge=0)

    @classmethod
    def from_viewer_count(cls, viewer_count: int) -> "PaceConfig":
        """Create PaceConfig from current viewer count
        
        Args:
            viewer_count: Current number of viewers
            
        Returns:
            PaceConfig instance with appropriate parameters
        """
        for level_name, config in PACE_MAPPING.items():
            if config["viewer_min"] <= viewer_count <= config["viewer_max"]:
                return cls(
                    level=PaceLevel(level_name),
                    words_per_minute=config["words_per_minute"],
                    pause_duration_ms=config["pause_duration_ms"],
                    segment_length_words=config["segment_length_words"],
                    qa_frequency_ratio=config["qa_frequency_ratio"],
                    viewer_count=viewer_count,
                )

        # Fallback to medium pace if no match
        medium_config = PACE_MAPPING["medium"]
        return cls(
            level=PaceLevel.MEDIUM,
            words_per_minute=medium_config["words_per_minute"],
            pause_duration_ms=medium_config["pause_duration_ms"],
            segment_length_words=medium_config["segment_length_words"],
            qa_frequency_ratio=medium_config["qa_frequency_ratio"],
            viewer_count=viewer_count,
        )


class QASegment(BaseModel):
    """Represents a Q&A segment in the stream (based on design.md)
    
    TODO: LEGACY - 过度抽象的LangGraph状态机概念
    新架构中Q&A处理简化为：问题文本 + 答案文本 + 基础元数据
    复杂的置信度评分、来源追踪等功能暂时保留向后兼容
    """
    segment_id: str
    question_text: str = Field(..., description="Original question text")
    answer: str = Field(..., description="Generated answer text")
    # TODO: LEGACY - 置信度评分属于AI评估逻辑，可简化
    confidence_score: float = Field(ge=0.0, le=1.0, default=0.0)
    # TODO: LEGACY - 来源追踪过于复杂，新架构简化
    sources_used: list[str] = Field(default_factory=list)
    processing_time_ms: int = Field(ge=0, default=0)
    # TODO: LEGACY - 过渡句生成属于内容生成逻辑，可独立
    transition_in: Optional[str] = Field(default=None)
    transition_out: Optional[str] = Field(default=None)
    estimated_duration_sec: int = Field(default=30, ge=1)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class LiveStreamState(BaseModel):
    """Central state container for LangGraph state machine
    
    Contains 13 essential fields across 4 categories as specified in design.
    All fields are typed and documented for clarity.
    """

    # === Content Category (4 fields) ===
    # TODO: LEGACY - LangGraph状态机字段，新架构中由session直接管理内容
    plan_outline: list[str] = Field(
        default_factory=list,
        description="Generated narrative outline with main talking points [LEGACY: 将移至session管理]"
    )
    current_segment_idx: int = Field(
        default=0,
        ge=0,
        description="Index of current segment being narrated [保留：前端API需要]"
    )
    # TODO: LEGACY - 主题追踪逻辑过于复杂，新架构简化
    key_points_covered: list[str] = Field(
        default_factory=list,
        description="Topics already discussed to avoid repetition [LEGACY: 状态机概念]"
    )
    # TODO: LEGACY - 预规划主题列表，违反新架构无全局状态原则
    pending_topics: list[str] = Field(
        default_factory=list,
        description="Remaining topics to be covered in stream [LEGACY: 状态机专用]"
    )

    # === Interaction Category (3 fields) ===
    # TODO: LEGACY - 已被LiveStreamSession.question_queue (asyncio.Queue) 替代
    question_queue: Annotated[list[dict[str, Any]], Field(
        default_factory=list,
        description="Prioritized queue of viewer questions to answer [LEGACY: 使用asyncio.Queue]"
    )]
    # TODO: LEGACY - 对话流上下文，LangGraph状态机概念，新架构简化
    conversation_flow: list[str] = Field(
        default_factory=list,
        description="Recent conversation context for coherent transitions [LEGACY: 状态机专用]"
    )
    last_interaction_time: datetime | None = Field(
        default=None,
        description="Timestamp of last viewer interaction for proactive engagement [可保留]"
    )

    # === Context Category (3 fields) ===
    current_persona: str = Field(
        default="default",
        description="Active persona configuration name [可保留：配置需要]"
    )
    # TODO: LEGACY - 时间上下文字典，LangGraph复杂概念，可简化为枚举
    time_context: dict[str, Any] = Field(
        default_factory=dict,
        description="Current time-based context (morning/afternoon/evening) [LEGACY: 过度复杂]"
    )
    pace_config: PaceConfig | None = Field(
        default=None,
        description="Dynamic pace parameters based on viewer count [保留：核心配置]"
    )

    # === Playback Tracking (新增播放状态追踪字段) ===
    # TODO: LEGACY - 检查点管理已被SentenceLevelTTSPlayer内部状态替代
    current_script_id: Optional[str] = Field(
        default=None,
        description="当前播放脚本ID，用于检查点恢复 [LEGACY: 已由播放器管理]"
    )
    # TODO: LEGACY - 句子索引追踪已迁移至播放器内部
    current_sentence_index: int = Field(
        default=0,
        ge=0,
        description="当前播放句子索引，用于精确中断恢复 [LEGACY: 播放器负责]"
    )
    # TODO: LEGACY - 字符级恢复过于精细，新架构简化为句级
    current_character_offset: int = Field(
        default=0,
        ge=0,
        description="当前句子内字符偏移，用于字符级恢复 [LEGACY: 过度复杂]"
    )
    # TODO: LEGACY - 内容缓存违反新架构原则
    remaining_content: List[str] = Field(
        default_factory=list,
        description="剩余要播放的内容段落列表 [LEGACY: 全局状态]"
    )
    # TODO: LEGACY - 检查点数据结构，已被InterruptionPoint替代
    playback_checkpoint: Optional[Dict[str, Any]] = Field(
        default=None,
        description="当前播放检查点数据 [LEGACY: 使用InterruptionPoint]"
    )
    
    # === Control Category (3 fields) ===
    stream_status: StreamStatus = Field(
        default=StreamStatus.INITIALIZING,
        description="Current operational status of the live stream"
    )
    break_until: datetime | None = Field(
        default=None,
        description="Timestamp when current break/pause ends"
    )
    segments_played_count: int = Field(
        default=0,
        ge=0,
        description="Counter of completed narrative segments"
    )

    # === Q&A Integration (based on design.md) ===
    # TODO: LEGACY - 复杂的Q&A状态管理，新架构简化为session内处理
    current_qa_segment: Optional[QASegment] = Field(
        default=None,
        description="Currently processing Q&A segment [LEGACY: 状态机概念]"
    )
    qa_session_count: int = Field(
        default=0,
        ge=0,
        description="Number of Q&A sessions completed [可保留：统计需要]"
    )
    last_qa_timestamp: Optional[datetime] = Field(
        default=None,
        description="Timestamp of last Q&A processing [可保留：API需要]"
    )
    
    # === Flow control for Q&A ===
    # TODO: LEGACY - Q&A流控制逻辑，已被PriorityAudioManager替代
    is_processing_qa: bool = Field(
        default=False,
        description="Flag indicating Q&A is currently being processed [LEGACY: 使用播放器状态]"
    )
    allow_qa_interruptions: bool = Field(
        default=True,
        description="Whether Q&A can interrupt current content [LEGACY: 配置化]"
    )
    qa_cooldown_until: Optional[datetime] = Field(
        default=None,
        description="Timestamp until which Q&A processing is on cooldown [LEGACY: 简化逻辑]"
    )

    # === Additional metadata (not part of core 13) ===
    form_id: str | None = Field(
        default=None,
        description="Source form ID for script content reference"
    )
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    def update_pace_from_viewers(self, viewer_count: int) -> None:
        """Update pace configuration based on current viewer count
        
        Args:
            viewer_count: Current number of viewers
        """
        self.pace_config = PaceConfig.from_viewer_count(viewer_count)
        self.updated_at = datetime.utcnow()

    def add_question(self, question_data: dict[str, Any]) -> None:
        """Add a new question to the queue with priority handling
        
        Args:
            question_data: Question details including text, priority, timestamp
        """
        self.question_queue.append(question_data)
        # Sort by priority (lower number = higher priority)
        self.question_queue.sort(key=lambda q: q.get("priority", 5))
        self.updated_at = datetime.utcnow()

    def advance_segment(self) -> bool:
        """Advance to next segment in the outline
        
        Returns:
            True if advanced successfully, False if at end
        """
        if self.current_segment_idx < len(self.plan_outline) - 1:
            self.current_segment_idx += 1
            self.segments_played_count += 1
            self.updated_at = datetime.utcnow()
            return True
        return False

    def add_covered_topic(self, topic: str) -> None:
        """Mark a topic as covered to avoid repetition
        
        Args:
            topic: Topic or key point that was just discussed
        """
        if topic not in self.key_points_covered:
            self.key_points_covered.append(topic)
            # Remove from pending if present
            if topic in self.pending_topics:
                self.pending_topics.remove(topic)
        self.updated_at = datetime.utcnow()

    def set_break_until(self, break_duration_ms: int) -> None:
        """Set a timed break/pause
        
        Args:
            break_duration_ms: Duration of break in milliseconds
        """
        from datetime import timedelta
        self.break_until = datetime.utcnow() + timedelta(milliseconds=break_duration_ms)
        self.updated_at = datetime.utcnow()

    def is_on_break(self) -> bool:
        """Check if currently in a timed break
        
        Returns:
            True if still on break, False if break has ended
        """
        if self.break_until is None:
            return False
        return datetime.utcnow() < self.break_until

    def get_progress_percentage(self) -> float:
        """Calculate stream progress as percentage
        
        Returns:
            Progress percentage (0.0 to 100.0)
        """
        if not self.plan_outline:
            return 0.0
        return (self.current_segment_idx / len(self.plan_outline)) * 100.0

    def can_process_qa(self) -> bool:
        """Check if system can process Q&A now (based on design.md)
        
        Returns:
            True if Q&A processing is allowed
        """
        # Check cooldown period
        if self.qa_cooldown_until and datetime.utcnow() < self.qa_cooldown_until:
            return False
        
        # Check if interruptions are allowed and not already processing
        return self.allow_qa_interruptions and not self.is_processing_qa

    def start_qa_processing(self, qa_segment: QASegment) -> None:
        """Mark start of Q&A processing
        
        Args:
            qa_segment: The Q&A segment being processed
        """
        self.is_processing_qa = True
        self.current_qa_segment = qa_segment
        self.updated_at = datetime.utcnow()

    def finish_qa_processing(self) -> None:
        """Mark completion of Q&A processing and trigger resume"""
        self.is_processing_qa = False
        self.current_qa_segment = None
        self.qa_session_count += 1
        self.last_qa_timestamp = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # 触发状态恢复回调（如果已设置）
        if hasattr(self, '_qa_completion_callback') and self._qa_completion_callback:
            try:
                # 异步调用恢复回调
                import asyncio
                if asyncio.iscoroutinefunction(self._qa_completion_callback):
                    # 创建任务来处理异步回调
                    try:
                        # 尝试获取当前运行的事件循环
                        loop = asyncio.get_running_loop()
                        # 在当前事件循环中创建任务
                        loop.create_task(self._qa_completion_callback(self))
                    except RuntimeError:
                        # 没有运行的事件循环，使用线程池或者记录警告
                        import logging
                        logging.warning("Cannot execute async Q&A callback: no running event loop")
                        # 不使用 asyncio.run()，因为它会与已有的事件循环冲突
                else:
                    # 同步回调
                    self._qa_completion_callback(self)
            except Exception as e:
                # 使用日志记录错误，但不影响主流程
                import logging
                logging.error(f"Error in Q&A completion callback: {e}")
    
    def set_qa_completion_callback(self, callback):
        """设置Q&A完成时的回调函数
        
        Args:
            callback: 回调函数，接收state参数
        """
        self._qa_completion_callback = callback

    def set_qa_cooldown(self, cooldown_seconds: int) -> None:
        """Set Q&A cooldown period
        
        Args:
            cooldown_seconds: Cooldown duration in seconds
        """
        from datetime import timedelta
        self.qa_cooldown_until = datetime.utcnow() + timedelta(seconds=cooldown_seconds)
        self.updated_at = datetime.utcnow()

    def to_dict(self) -> dict[str, Any]:
        """Convert state to dictionary for serialization"""
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "LiveStreamState":
        """Create state instance from dictionary"""
        return cls.model_validate(data)


# ========================================================================
# 新架构模型 - 轻量级替代方案
# ========================================================================

class StreamSessionConfig(BaseModel):
    """新架构的轻量级会话配置模型
    
    设计原则：
    1. 最小必要字段集合
    2. 无全局状态管理 
    3. 纯配置数据，不包含运行时状态
    
    迁移路径：
    - 替代LiveStreamState中的配置相关字段
    - 配合LiveStreamSession的asyncio.Queue使用
    - 面向session-based架构设计
    """
    
    # === 基础标识 ===
    session_id: str = Field(..., description="会话唯一标识符")
    
    # === 核心状态（最小集合）===
    status: StreamStatus = Field(
        default=StreamStatus.INITIALIZING,
        description="当前流状态（保留：前端API需要）"
    )
    current_segment_idx: int = Field(
        default=0,
        ge=0,
        description="当前段落索引（保留：进度显示需要）"
    )
    
    # === 动态配置 ===
    pace_config: Optional[PaceConfig] = Field(
        default=None,
        description="动态节奏配置（保留：核心功能）"
    )
    current_persona: str = Field(
        default="default",
        description="当前人格配置（保留：内容生成需要）"
    )
    
    # === 元数据 ===
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # === 统计信息（向后兼容）===
    segments_played_count: int = Field(
        default=0,
        ge=0,
        description="已播放段落计数（保留：统计需要）"
    )
    qa_session_count: int = Field(
        default=0,
        ge=0,
        description="Q&A会话计数（保留：统计需要）"
    )
    last_qa_timestamp: Optional[datetime] = Field(
        default=None,
        description="最后Q&A时间（保留：API需要）"
    )
    
    def update_pace_from_viewers(self, viewer_count: int) -> None:
        """基于观众数量更新节奏配置"""
        self.pace_config = PaceConfig.from_viewer_count(viewer_count)
        self.updated_at = datetime.utcnow()
    
    def advance_segment(self) -> bool:
        """推进到下一段落"""
        self.current_segment_idx += 1
        self.segments_played_count += 1
        self.updated_at = datetime.utcnow()
        return True
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典（API兼容）"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "StreamSessionConfig":
        """从字典创建实例"""
        return cls.model_validate(data)
    
    @classmethod
    def from_legacy_state(cls, legacy_state: "LiveStreamState") -> "StreamSessionConfig":
        """从遗留LiveStreamState创建新配置（迁移辅助）"""
        return cls(
            session_id=legacy_state.form_id or f"session_{int(datetime.utcnow().timestamp())}",
            status=legacy_state.stream_status,
            current_segment_idx=legacy_state.current_segment_idx,
            pace_config=legacy_state.pace_config,
            current_persona=legacy_state.current_persona,
            segments_played_count=legacy_state.segments_played_count,
            qa_session_count=legacy_state.qa_session_count,
            last_qa_timestamp=legacy_state.last_qa_timestamp,
            created_at=legacy_state.created_at,
            updated_at=legacy_state.updated_at,
        )


class SimpleQARequest(BaseModel):
    """简化的Q&A请求模型（替代复杂的QASegment）"""
    
    question_id: str = Field(..., description="问题唯一标识")
    question_text: str = Field(..., description="问题文本")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    priority: int = Field(default=5, ge=1, le=10, description="优先级（1最高）")
    
    # 可选的处理结果字段
    answer_text: Optional[str] = Field(default=None, description="生成的答案")
    processing_time_ms: Optional[int] = Field(default=None, description="处理时长")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
