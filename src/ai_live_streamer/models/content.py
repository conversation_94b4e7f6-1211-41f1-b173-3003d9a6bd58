"""Content models for questions, segments, and script sections

Handles viewer questions with priority queuing, narrative segments with
context tracking, and script section categorization for Style Corpus.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class QuestionPriority(str, Enum):
    """Question priority levels for queue management"""
    URGENT = "urgent"      # Technical issues, complaints
    HIGH = "high"        # Product questions, pricing  
    MEDIUM = "medium"      # General inquiries
    LOW = "low"         # Casual comments
    DEFERRED = "deferred"    # Non-essential, can be ignored


class QuestionStatus(Enum):
    """Question processing status"""
    PENDING = "pending"
    PROCESSING = "processing" 
    ANSWERED = "answered"
    DEFERRED = "deferred"
    EXPIRED = "expired"


class Question(BaseModel):
    """Viewer question with priority and context management"""
    
    question_id: str = Field(description="Unique question identifier")
    text: str = Field(description="Original question text from viewer")
    viewer_name: Optional[str] = Field(default=None, description="Name of viewer who asked")
    
    # Priority and routing
    priority: QuestionPriority = Field(
        default=QuestionPriority.MEDIUM,
        description="Question priority for queue ordering"
    )
    status: QuestionStatus = Field(
        default=QuestionStatus.PENDING,
        description="Current processing status"
    )
    
    # Timing information
    asked_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="When question was originally asked"
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        description="When question expires if not answered"
    )
    processing_started_at: Optional[datetime] = Field(
        default=None,
        description="When question processing began"
    )
    answered_at: Optional[datetime] = Field(
        default=None,
        description="When question was answered"
    )
    
    # Processing metadata
    interrupt_allowed: bool = Field(
        default=False,
        description="Whether this question can interrupt current narration"
    )
    requires_real_time_data: bool = Field(
        default=False,
        description="Whether answer requires real-time API calls (price/stock)"
    )
    
    # Context and categorization
    topic_tags: List[str] = Field(
        default_factory=list,
        description="Topic tags for question categorization"
    )
    related_products: List[str] = Field(
        default_factory=list,
        description="Product SKUs mentioned in question"
    )
    
    # Answer tracking
    answer_text: Optional[str] = Field(
        default=None,
        description="Generated answer text"
    )
    retrieval_context: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="RAG retrieval context used for answer"
    )
    confidence_score: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Confidence score of generated answer"
    )
    
    def mark_processing_started(self) -> None:
        """Mark question as starting to be processed"""
        self.status = QuestionStatus.PROCESSING
        self.processing_started_at = datetime.utcnow()
    
    def mark_answered(self, answer_text: str, confidence_score: float) -> None:
        """Mark question as answered
        
        Args:
            answer_text: Generated answer
            confidence_score: Answer confidence (0.0-1.0)
        """
        self.answer_text = answer_text
        self.confidence_score = confidence_score
        self.status = QuestionStatus.ANSWERED
        self.answered_at = datetime.utcnow()
    
    def is_expired(self) -> bool:
        """Check if question has expired
        
        Returns:
            True if expired, False otherwise
        """
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def get_processing_time_ms(self) -> Optional[int]:
        """Get question processing time in milliseconds
        
        Returns:
            Processing time or None if not completed
        """
        if self.processing_started_at and self.answered_at:
            delta = self.answered_at - self.processing_started_at
            return int(delta.total_seconds() * 1000)
        return None


class SegmentType(Enum):
    """Types of narrative segments"""
    OPENING = "opening"           # Stream introduction
    TOPIC_INTRO = "topic_intro"   # New topic introduction
    MAIN_CONTENT = "main_content" # Primary content delivery
    TRANSITION = "transition"     # Between topics
    QA_RESPONSE = "qa_response"   # Question answering
    CLOSING = "closing"           # Stream conclusion
    BREAK = "break"              # Scheduled break


class Segment(BaseModel):
    """Individual narrative segment with context and timing"""
    
    segment_id: str = Field(description="Unique segment identifier")
    segment_type: SegmentType = Field(description="Type of content in this segment")
    title: str = Field(description="Human-readable segment title")
    content: str = Field(description="Main text content for narration")
    
    # Timing and duration
    planned_duration_seconds: int = Field(
        ge=10,
        le=600,
        description="Expected duration in seconds"
    )
    actual_duration_seconds: Optional[int] = Field(
        default=None,
        description="Actual playback duration"
    )
    
    # Content metadata
    key_points: List[str] = Field(
        default_factory=list,
        description="Main talking points covered in this segment"
    )
    related_products: List[str] = Field(
        default_factory=list,
        description="Products mentioned in this segment"
    )
    call_to_actions: List[str] = Field(
        default_factory=list,
        description="CTAs included in this segment"
    )
    
    # Context tracking
    prerequisite_points: List[str] = Field(
        default_factory=list,
        description="Points that should be covered before this segment"
    )
    follow_up_topics: List[str] = Field(
        default_factory=list,
        description="Natural follow-up topics after this segment"
    )
    
    # Persona and style
    persona_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Persona-specific context for this segment"
    )
    style_hints: List[str] = Field(
        default_factory=list,
        description="Style guidance for TTS and delivery"
    )
    
    # Playback tracking
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    is_played: bool = Field(default=False)
    
    def mark_started(self) -> None:
        """Mark segment as started playing"""
        self.started_at = datetime.utcnow()
    
    def mark_completed(self) -> None:
        """Mark segment as completed"""
        self.completed_at = datetime.utcnow()
        self.is_played = True
        
        if self.started_at:
            duration = self.completed_at - self.started_at
            self.actual_duration_seconds = int(duration.total_seconds())


class ScriptSectionType(Enum):
    """Script section categories for Style Corpus"""
    OPENING = "opening"               # 开场
    SELLING_POINT = "selling_point"   # 卖点
    OBJECTION_HANDLING = "objection_handling"  # 异议处理  
    CLOSING = "closing"               # 收尾
    CTA = "cta"                      # Call to Action
    TRANSITION = "transition"         # 过渡语句
    INTERACTION = "interaction"       # 互动引导


class ScriptSection(BaseModel):
    """Script section for Style Corpus indexing and retrieval"""
    
    section_id: str = Field(description="Unique section identifier")
    section_type: ScriptSectionType = Field(description="Category of script section")
    content: str = Field(description="Script text content")
    
    # Style metadata
    tone_tags: List[str] = Field(
        default_factory=list,
        description="Tone characteristics (friendly, professional, urgent)"
    )
    style_features: Dict[str, Any] = Field(
        default_factory=dict,
        description="Style features for matching (sentence_length, complexity, etc.)"
    )
    
    # Usage context
    suitable_contexts: List[str] = Field(
        default_factory=list,
        description="Contexts where this section works well"
    )
    time_contexts: List[str] = Field(
        default_factory=list,
        description="Time periods when this style is appropriate"
    )
    
    # Performance metrics
    usage_count: int = Field(
        default=0,
        ge=0,
        description="How many times this section has been used"
    )
    effectiveness_score: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Effectiveness score based on viewer engagement"
    )
    
    # Source information
    source_document: Optional[str] = Field(
        default=None,
        description="Original document this section came from"
    )
    extracted_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="When this section was extracted and indexed"
    )
    
    def increment_usage(self) -> None:
        """Increment usage counter"""
        self.usage_count += 1
    
    def update_effectiveness(self, score: float) -> None:
        """Update effectiveness score
        
        Args:
            score: New effectiveness score (0.0-1.0)
        """
        if 0.0 <= score <= 1.0:
            self.effectiveness_score = score


class ContentLibrary(BaseModel):
    """Collection of script sections organized by type and context"""
    
    sections: Dict[str, ScriptSection] = Field(
        default_factory=dict,
        description="Script sections indexed by section_id"
    )
    
    def add_section(self, section: ScriptSection) -> None:
        """Add a script section to the library
        
        Args:
            section: ScriptSection to add
        """
        self.sections[section.section_id] = section
    
    def get_sections_by_type(self, section_type: ScriptSectionType) -> List[ScriptSection]:
        """Get all sections of a specific type
        
        Args:
            section_type: Type of sections to retrieve
            
        Returns:
            List of matching ScriptSection instances
        """
        return [
            section for section in self.sections.values()
            if section.section_type == section_type
        ]
    
    def get_sections_by_context(self, context: str) -> List[ScriptSection]:
        """Get sections suitable for a specific context
        
        Args:
            context: Context name to match
            
        Returns:
            List of suitable ScriptSection instances
        """
        return [
            section for section in self.sections.values()
            if context in section.suitable_contexts
        ]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get library statistics
        
        Returns:
            Dictionary with library metrics
        """
        total_sections = len(self.sections)
        by_type = {}
        total_usage = 0
        
        for section in self.sections.values():
            section_type = section.section_type.value
            by_type[section_type] = by_type.get(section_type, 0) + 1
            total_usage += section.usage_count
        
        return {
            "total_sections": total_sections,
            "by_type": by_type,
            "total_usage": total_usage,
            "average_usage": total_usage / total_sections if total_sections > 0 else 0,
        }


class QAContext(BaseModel):
    """QA processing context"""
    
    persona: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Persona configuration for response generation"
    )
    question: Question = Field(description="The question to be answered")
    sku_id: str = Field(description="Product SKU ID")
    product_name: str = Field(description="Product name")
    conversation_history: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Previous conversation history"
    )
    max_response_time_sec: int = Field(
        default=30,
        description="Maximum allowed response time in seconds"
    )


class QAResponse(BaseModel):
    """QA processing response"""
    
    answer: str = Field(description="Generated answer text")
    confidence: float = Field(
        ge=0.0,
        le=1.0,
        description="Confidence score of the answer"
    )
    sources: List[str] = Field(
        default_factory=list,
        description="Information sources used for the answer"
    )
    response_type: str = Field(
        default="normal",
        description="Type of response (normal, error, timeout, etc.)"
    )
    follow_up_suggestions: List[str] = Field(
        default_factory=list,
        description="Suggested follow-up questions"
    )
    processing_time_ms: Optional[int] = Field(
        default=None,
        description="Processing time in milliseconds"
    )