"""AI直播系统 v2.0 - 应用启动入口

基于服务器端智能播放列表的新一代直播系统。

Features:
- 服务器端智能播放列表管理
- WebSocket v2 请求-响应协议
- 智能QA插入和时机控制
- 主动预合成和缓存优化
- 状态恢复和断线重连
- 实时监控和健康检查

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import time
import logging
from loguru import logger
from pathlib import Path

# 依赖注入和核心组件
from .core.dependencies import startup_components, shutdown_components, get_component_status
from .core.streaming_config import get_streaming_config
from .core.config import cfg
from .core.exceptions import ServiceError

# API路由
from .api.websocket_routes_v2 import router as websocket_v2_router
from .api.control import router as control_router  # 保留原有的控制API
from .api.console import console_router  # 控制台API
from .api.operational_forms import operational_router  # 运营表单API
from .api.script_preview import script_preview_router  # 脚本预览API
from .api.auth import auth_router  # 认证API
# V1 audio_websocket removed - using V2 WebSocket
# from .api.audio_websocket import audio_ws_router  # 音频WebSocket
from .api.status_websocket import router as status_ws_router  # 状态WebSocket
from .api.system_status import router as system_status_router  # 系统状态API
from .api.monitoring import monitoring_router  # 监控API

# 版本信息
__version__ = "2.0.0"
__description__ = "AI直播系统 v2.0 - 服务器端智能播放列表版本"


def configure_logging():
    """配置日志过滤器，过滤掉特定的HTTP访问日志"""
    class AccessLogFilter(logging.Filter):
        def filter(self, record):
            # 过滤掉特定的HTTP访问日志
            if hasattr(record, 'getMessage'):
                message = record.getMessage()
                # 过滤掉频繁的状态检查端点
                if any(endpoint in message for endpoint in [
                    'GET /api/status',
                    'GET /api/control/question-queue',
                    'GET /health',
                    'GET /api/v2/monitoring'
                ]):
                    return False
            return True

    # 应用过滤器到uvicorn的访问日志记录器
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.addFilter(AccessLogFilter())


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info(f"🚀 启动 AI直播系统 v{__version__}")
    
    # 配置日志过滤
    configure_logging()
    logger.info("🔧 日志配置已应用")
    
    try:
        # 初始化TTS引擎（V2依赖系统需要）
        from .core.dependencies import set_tts_engine
        from .services.factories import ServiceFactory
        
        logger.info("🎵 正在初始化TTS引擎...")
        tts_engine = await ServiceFactory.create_tts_engine()
        set_tts_engine(tts_engine)
        logger.info(f"✅ TTS引擎已初始化并注入: {type(tts_engine).__name__}")
        
        # 启动核心组件
        await startup_components()
        logger.info("✅ 所有核心组件启动完成")
        
        yield
        
    except asyncio.CancelledError:
        # 正常的关闭流程触发的取消，不需要记录为错误
        logger.debug("收到关闭信号，正在优雅关闭...")
        raise
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    finally:
        logger.info("🛑 开始关闭应用...")
        try:
            await shutdown_components()
            logger.info("✅ 所有组件已安全关闭")
        except asyncio.CancelledError:
            # 关闭过程中的取消是正常的
            logger.debug("关闭过程被取消（正常行为）")
        except Exception as e:
            logger.error(f"关闭组件时发生错误: {e}")


# 创建FastAPI应用
app = FastAPI(
    title="AI直播系统",
    description=__description__,
    version=__version__,
    docs_url="/docs" if cfg.debug else None,
    redoc_url="/redoc" if cfg.debug else None,
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加GZip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 请求计时中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间到响应头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# 注册路由
app.include_router(websocket_v2_router)
app.include_router(control_router)
app.include_router(console_router)
app.include_router(operational_router)
app.include_router(script_preview_router)
app.include_router(auth_router)
# V1 audio_ws_router removed
# app.include_router(audio_ws_router)
app.include_router(status_ws_router)
app.include_router(system_status_router)
app.include_router(monitoring_router)

# 挂载静态文件
static_path = Path(__file__).parent / "api" / "templates" / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
    logger.info(f"✅ 静态文件已挂载: {static_path}")


# 基础API端点

@app.get("/", response_class=HTMLResponse)
async def serve_admin_console():
    """提供原有的运营控制台页面"""
    template_path = Path(__file__).parent / "api" / "templates" / "admin_console.html"
    if template_path.exists():
        with open(template_path, "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    else:
        # 如果找不到原页面，返回V2系统信息作为fallback
        return JSONResponse({
            "name": "AI直播系统",
            "version": __version__,
            "description": __description__,
            "api_version": "v2.0",
            "features": [
                "服务器端智能播放列表",
                "WebSocket v2 协议",
                "智能QA插入",
                "主动预合成",
                "状态恢复",
                "实时监控"
            ]
        })


@app.get("/config", response_class=HTMLResponse)
async def serve_config_form():
    """提供运营配置表单页面"""
    template_path = Path(__file__).parent / "api" / "templates" / "operational_form.html"
    if template_path.exists():
        with open(template_path, "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    else:
        return HTMLResponse(
            content="<h1>AI Live Streamer</h1><p>Configuration form not found</p>",
            status_code=404
        )


@app.get("/control", response_class=HTMLResponse)
async def serve_live_control_panel():
    """提供直播控制面板页面"""
    template_path = Path(__file__).parent / "api" / "templates" / "live_control_panel.html"
    if template_path.exists():
        with open(template_path, "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    else:
        return HTMLResponse(
            content="<h1>AI Live Streamer</h1><p>Live control panel not found</p>",
            status_code=404
        )


@app.get("/dashboard")
async def dashboard():
    """V2控制台页面 - 新的V2功能展示页面"""
    template_path = Path(__file__).parent / "api" / "templates" / "v2_dashboard.html"
    with open(template_path, "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 获取组件状态
        component_status = get_component_status()
        
        # 获取事件总线状态
        from .services.synthesis_event_bus import get_synthesis_event_bus
        event_bus = get_synthesis_event_bus()
        event_bus_stats = event_bus.get_stats()
        
        # 计算整体健康状态
        all_components_up = all(component_status.values())
        
        status = "healthy" if all_components_up else "degraded"
        
        return JSONResponse(
            status_code=200 if all_components_up else 503,
            content={
                "status": status,
                "version": __version__,
                "timestamp": time.time(),
                "environment": cfg.app_env,
                "components": component_status,
                "checks": {
                    "all_components_initialized": all_components_up,
                    "core_services_ready": component_status.get("playlist_manager", False) and 
                                          component_status.get("client_tracker", False) and
                                          component_status.get("content_provider", False),
                    "broadcast_system_ready": component_status.get("state_broadcaster", False),
                    "event_bus_ready": event_bus_stats["is_running"] and event_bus_stats["has_handler"]
                }
            }
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": __import__('time').time()
            }
        )


@app.get("/api/v2/info")
async def get_v2_info():
    """获取V2系统基本信息（JSON格式）"""
    return {
        "name": "AI直播系统",
        "version": __version__,
        "description": __description__,
        "api_version": "v2.0",
        "features": [
            "服务器端智能播放列表",
            "WebSocket v2 协议",
            "智能QA插入",
            "主动预合成",
            "状态恢复",
            "实时监控"
        ],
        "endpoints": {
            "home": "/",
            "config": "/config",
            "control": "/control",
            "v2_dashboard": "/dashboard",
            "health": "/health",
            "system_info": "/api/v2/system/info",
            "websocket_v2": "/ws/v2/stream"
        }
    }


@app.get("/api/v2/system/info")
async def get_system_info():
    """获取系统详细信息"""
    try:
        from .core.dependencies import (
            get_playlist_manager, get_client_state_tracker,
            get_content_provider, get_proactive_synthesizer,
            get_websocket_v2_handler
        )
        
        # 获取各组件的统计信息
        info = {
            "system": {
                "version": __version__,
                "uptime_seconds": 0,  # 可以添加启动时间跟踪
                "architecture": "server_side_smart_playlist"
            }
        }
        
        # 安全地获取组件信息
        try:
            playlist_manager = get_playlist_manager()
            playlist_info = await playlist_manager.get_playlist_info()
            info["playlist"] = playlist_info.to_dict()
        except Exception as e:
            info["playlist"] = {"error": str(e)}
            
        try:
            client_tracker = get_client_state_tracker()
            client_stats = await client_tracker.get_aggregated_stats()
            info["clients"] = client_stats.to_dict()
        except Exception as e:
            info["clients"] = {"error": str(e)}
            
        try:
            content_provider = get_content_provider()
            provider_stats = await content_provider.get_stats()
            info["content_provider"] = provider_stats
        except Exception as e:
            info["content_provider"] = {"error": str(e)}
            
        try:
            proactive_synthesizer = get_proactive_synthesizer()
            synthesis_stats = await proactive_synthesizer.get_stats()
            info["proactive_synthesis"] = synthesis_stats
        except Exception as e:
            info["proactive_synthesis"] = {"error": str(e)}
            
        try:
            websocket_handler = get_websocket_v2_handler()
            connection_stats = await websocket_handler.get_connection_stats()
            info["websocket_connections"] = connection_stats
        except Exception as e:
            info["websocket_connections"] = {"error": str(e)}
            
        try:
            from .core.dependencies import get_state_broadcaster
            state_broadcaster = get_state_broadcaster()
            broadcaster_stats = state_broadcaster.get_stats()
            info["state_broadcaster"] = broadcaster_stats
        except Exception as e:
            info["state_broadcaster"] = {"error": str(e)}
            
        try:
            from .core.dependencies import get_system_monitor
            system_monitor = get_system_monitor()
            monitor_stats = system_monitor.get_stats()
            info["system_monitor"] = monitor_stats
        except Exception as e:
            info["system_monitor"] = {"error": str(e)}
            
        return info
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


@app.get("/api/v2/config")
async def get_config_info():
    """获取配置信息"""
    try:
        config = get_streaming_config()
        
        # 返回非敏感的配置信息
        return {
            "streaming": {
                "buffer_thresholds": {
                    "healthy_ms": config.client_buffer_healthy_ms,
                    "at_risk_ms": config.client_buffer_at_risk_ms
                },
                "qa_insertion": {
                    "strategy": config.qa_insertion_strategy,
                    "min_safe_delay_ms": config.qa_insertion_min_safe_delay_ms
                },
                "proactive_synthesis": {
                    "enabled": config.proactive_synthesis_enabled,
                    "max_concurrent": config.proactive_max_concurrent,
                    "look_ahead_items": config.proactive_look_ahead_items
                },
                "caching": {
                    "max_size_mb": config.tts_cache_max_size_mb,
                    "ttl_hours": config.tts_cache_ttl_hours
                }
            },
            "version": __version__
        }
        
    except Exception as e:
        logger.error(f"获取配置信息失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


@app.post("/api/v2/system/reset")
async def reset_system():
    """重置系统状态（调试用）"""
    try:
        from .core.dependencies import (
            get_playlist_manager, get_client_state_tracker,
            get_content_provider, get_proactive_synthesizer
        )
        
        # 重置各组件状态
        await get_playlist_manager().reset()
        await get_client_state_tracker().reset()
        await get_content_provider().reset_stats()
        
        # 清理预合成器的缓存
        proactive_synthesizer = get_proactive_synthesizer()
        if proactive_synthesizer.is_running:
            await proactive_synthesizer.clear_processed_cache()
            
        logger.info("系统状态已重置")
        return {"status": "success", "message": "系统状态已重置"}
        
    except Exception as e:
        logger.error(f"重置系统失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


# 开发模式专用端点

@app.get("/api/v2/monitoring/health")
async def get_health_report():
    """获取综合健康报告"""
    try:
        from .core.dependencies import get_system_monitor
        system_monitor = get_system_monitor()
        health_report = await system_monitor.get_comprehensive_health_report()
        
        return health_report
        
    except Exception as e:
        logger.error(f"获取健康报告失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


@app.get("/api/v2/monitoring/metrics")
async def get_performance_metrics():
    """获取性能指标"""
    try:
        from .core.dependencies import get_system_monitor
        system_monitor = get_system_monitor()
        metrics = await system_monitor.get_performance_metrics()
        
        return metrics
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


@app.get("/api/v2/debug/components")
async def debug_components():
    """调试：获取所有组件状态"""
    try:
        from .core.dependencies import (
            get_playlist_manager, get_client_state_tracker,
            get_content_provider, get_proactive_synthesizer,
            get_websocket_v2_handler, get_system_monitor
        )
        
        debug_info = {}
        
        # 安全地收集每个组件的调试信息
        components = [
            ("playlist_manager", get_playlist_manager, "get_debug_info"),
            ("client_tracker", get_client_state_tracker, "get_debug_info"),
            ("proactive_synthesizer", get_proactive_synthesizer, "get_stats"),
            ("system_monitor", get_system_monitor, "get_stats"),
        ]
        
        for component_name, getter, method_name in components:
            try:
                component = getter()
                if hasattr(component, method_name):
                    debug_info[component_name] = await getattr(component, method_name)()
                else:
                    debug_info[component_name] = {"error": f"Method {method_name} not found"}
            except Exception as e:
                debug_info[component_name] = {"error": str(e)}
                
        return debug_info
        
    except Exception as e:
        logger.error(f"获取调试信息失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


# 异常处理器

@app.exception_handler(ServiceError)
async def service_error_handler(request: Request, exc: ServiceError):
    """处理服务错误"""
    logger.error(f"服务错误 in {request.url}: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "service_error",
            "message": str(exc),
            "service_name": exc.service_name,
            "error_code": exc.error_code
        }
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常 in {request.url}: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "type": type(exc).__name__,
            "message": str(exc),
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


def create_app() -> FastAPI:
    """创建应用实例（用于测试）"""
    return app


def main():
    """主函数 - 启动应用"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI直播系统 v2.0")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", default=8000, type=int, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    parser.add_argument("--log-level", default="info", help="日志级别")
    
    args = parser.parse_args()
    
    logger.info(f"🚀 启动 AI直播系统 v{__version__}")
    logger.info(f"🌐 服务器地址: http://{args.host}:{args.port}")
    logger.info(f"📖 API文档: http://{args.host}:{args.port}/docs")
    logger.info(f"🔗 WebSocket v2: ws://{args.host}:{args.port}/ws/v2/stream")
    
    uvicorn.run(
        "src.ai_live_streamer.app_v2:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level
    )


if __name__ == "__main__":
    main()