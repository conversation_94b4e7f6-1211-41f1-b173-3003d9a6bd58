<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播控制面板</title>
    <link rel="stylesheet" href="/static/css/live_control_panel.css?v=2.0.1">
    
    <!-- Session Manager - 统一的会话状态管理 -->
    <script src="/static/js/session_manager.js?v=1.0.0"></script>
    
    <!-- Web Audio Player V2 Modular Scripts -->
    <script src="/static/js/audio_config.js?v=2.2.0"></script>
    <script src="/static/js/connection.js?v=2.2.0"></script>
    <script src="/static/js/audio_player.js?v=2.2.0"></script>
    <script src="/static/js/protocol.js?v=2.2.0"></script>
    <script src="/static/js/web_audio_player_v2.js?v=2.2.0"></script>
    <script src="/static/js/player_loader.js?v=2.2.0"></script>
    
    <!-- WebSocket架构重构：新增的WebSocket管理模块 -->
    <script src="/static/js/websocket_schema.js?v=1.0.0"></script>
    <script src="/static/js/websocket_reconnect.js?v=1.0.0"></script>
    </head>
<body>
    <script src="/static/js/live_control_panel.js?v=2.0.1" defer></script>
    <div class="header">
        <div class="header-content">
            <div class="logo">🎬 AI直播控制面板</div>
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">系统离线</span>
            </div>
        </div>
    </div>

    <div class="container">

        <!-- Stream Source Info -->
        <div class="progress-container" id="streamSourceInfo" style="display: none;">
            <h4>🎬 直播脚本信息</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div>
                    <strong>脚本标题:</strong>
                    <div id="streamTitle" style="color: #667eea;">--</div>
                </div>
                <div>
                    <strong>产品:</strong>
                    <div id="productName" style="color: #667eea;">--</div>
                </div>
                <div>
                    <strong>公司:</strong>
                    <div id="companyName" style="color: #667eea;">--</div>
                </div>
                <div>
                    <strong>来源表单:</strong>
                    <div id="sourceFormId" style="color: #667eea; font-family: monospace; font-size: 12px;">--</div>
                </div>
                <div>
                    <strong>脚本段落数:</strong>
                    <div id="scriptSegmentsCount" style="color: #667eea;">--</div>
                </div>
                <div>
                    <strong>播放状态:</strong>
                    <div id="contentPlaybackStatus" style="color: #667eea; font-weight: 500;">--</div>
                </div>
            </div>
            <!-- 播放状态详情 -->
            <div id="playbackStatusDetails" style="margin-top: 15px; padding: 10px; background: #f8f9ff; border-radius: 6px; border-left: 4px solid #667eea; display: none;">
                <div style="font-size: 14px; color: #555;">
                    <div id="playbackStatusMessage" style="margin-bottom: 5px;"></div>
                    <div id="playbackWarning" style="color: #f39c12; font-size: 13px;"></div>
                </div>
            </div>
            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e1e5e9;">
                <a href="/" target="_blank" style="text-decoration: none; color: #667eea; font-size: 14px;">
                    ← 返回管理控制台
                </a>
            </div>
        </div>

        <!-- 当前播放内容与脚本预览 -->
        <div class="progress-container" id="currentContentInfo">
            <div class="collapsible-header" onclick="toggleScriptPreview()">
                <h4>🎤 当前播放内容与脚本预览</h4>
                <span class="collapse-icon">▶</span>
            </div>
            <div style="margin-top: 15px;">
                <div style="margin-bottom: 10px;">
                    <strong>段落标题:</strong>
                    <div id="currentSegmentTitle" style="color: #667eea; font-size: 16px; margin-top: 5px;">--</div>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>内容预览:</strong>
                    <div id="sentenceScrollContainer" style="background: #f8f9ff; padding: 10px; border-radius: 6px; margin-top: 5px; font-size: 14px; line-height: 1.4; max-height: 120px; overflow-y: auto; position: relative;">
                        <div id="currentSentenceDisplay" style="min-height: 40px;">
                            <div id="currentSentenceText" style="font-weight: 500; color: #2c3e50; margin-bottom: 5px;">--</div>
                            <div id="sentenceProgress" style="font-size: 12px; color: #7f8c8d;">等待开始播放...</div>
                        </div>
                        <div id="expandedContentContainer" style="display: none; margin-top: 10px; padding-top: 10px; border-top: 1px solid #e1e5e9;">
                            <div id="allSentencesList"></div>
                        </div>
                        <button id="toggleContentExpand" onclick="toggleContentExpansion()" style="position: absolute; top: 5px; right: 5px; background: none; border: none; color: #667eea; cursor: pointer; font-size: 12px; padding: 2px 5px;">展开</button>
                    </div>
                    <!-- 保留原有的简单预览作为fallback -->
                    <div id="currentContentPreview" style="display: none; background: #f8f9ff; padding: 10px; border-radius: 6px; margin-top: 5px; font-size: 14px; line-height: 1.4;">--</div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; font-size: 12px; color: #7f8c8d;">
                    <div>播放状态: <span id="audioPlaybackStatus">--</span></div>
                    <div>引擎类型: <span id="audioEngineType">--</span></div>
                    <div>播放进度: <span id="audioProgress">--</span></div>
                </div>
            </div>
            <div class="collapsible-content" id="scriptTimelineContent">
                <div class="script-timeline" id="scriptTimeline">
                    <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                        正在加载脚本内容...
                    </div>
                </div>
            </div>
        </div>

        <!-- 音频流控制面板 -->
        <div class="control-panel">
            <div class="control-section">
                <div class="section-title">音频流控制</div>
                <div class="control-buttons">
                    <button class="btn btn-success" id="startAudioStreamBtn" onclick="startAudioStreaming()">
                        🎵 启动音频流
                    </button>
                    <button class="btn btn-primary" id="enableAudioPlayBtn" onclick="enableAudioPlayback()" style="display: none;">
                        🔊 启用音频系统
                    </button>
                    <button class="btn btn-primary" onclick="testAudioStreaming()">
                        🧪 测试音频流
                    </button>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #7f8c8d;">
                    <span id="audioStreamInfo">音频流未启动</span>
                </div>
            </div>
        </div>

        <!-- 问题提交 -->
        <div class="question-section">
            <div class="section-title">观众问题管理</div>
            <div class="question-input">
                <input type="text" id="questionText" placeholder="输入观众问题..." maxlength="200">
                <select id="questionPriority">
                    <option value="low">低优先级</option>
                    <option value="medium" selected>中优先级</option>
                    <option value="high">高优先级</option>
                </select>
                <button class="btn btn-primary" id="questionSubmitBtn" onclick="submitQuestion()">提交问题</button>
            </div>
            <div class="question-stats" id="questionStats">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">0</div>
                        <div class="stat-label">总处理数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">0%</div>
                        <div class="stat-label">成功率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">0%</div>
                        <div class="stat-label">音频成功率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">0ms</div>
                        <div class="stat-label">平均响应时间</div>
                    </div>
                </div>
            </div>
            <div class="question-queue" id="questionQueue">
                <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                    暂无问题
                </div>
            </div>
        </div>
    </div>

    </body>
</html>