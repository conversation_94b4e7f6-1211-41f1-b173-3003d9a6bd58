<!DOCTYPE html>
<html>
<head>
    <title>WebAudioPlayer前端诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; background: #007bff; color: white; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log-entry { margin: 2px 0; padding: 3px; border-left: 3px solid #ddd; }
        .log-success { border-left-color: green; }
        .log-error { border-left-color: red; }
        .log-warning { border-left-color: orange; }
        .log-info { border-left-color: blue; }
    </style>
</head>
<body>
    <h1>🔍 WebAudioPlayer前端诊断工具</h1>
    
    <div class="test-section">
        <h2>环境检查</h2>
        <div id="environmentCheck"></div>
    </div>
    
    <div class="test-section">
        <h2>静态文件加载检查</h2>
        <div id="staticFileCheck"></div>
    </div>
    
    <div class="test-section">
        <h2>WebAudioPlayer初始化测试</h2>
        <div id="playerInitCheck"></div>
        <button onclick="testPlayerInitialization()">重新测试初始化</button>
    </div>
    
    <div class="test-section">
        <h2>WebSocket连接测试</h2>
        <div id="websocketCheck"></div>
        <button onclick="testWebSocketConnection()">测试WebSocket连接</button>
    </div>
    
    <div class="test-section">
        <h2>实时日志</h2>
        <div id="realTimeLog" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h2>修复验证</h2>
        <button onclick="runFullDiagnostic()">运行完整诊断</button>
        <button onclick="simulateControlPanelBehavior()">模拟控制面板行为</button>
    </div>

    <!-- Web Audio Player V2 Modular Scripts -->
    <script src="/static/js/connection.js?v=2.1.0"></script>
    <script src="/static/js/audio_player.js?v=2.1.0"></script>
    <script src="/static/js/protocol.js?v=2.1.0"></script>
    <script src="/static/js/web_audio_player_v2.js?v=2.1.0"></script>
    <script src="/static/js/player_loader.js?v=2.1.0"></script>
    <script>
        let diagnosticLog = [];
        let testPlayer = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = {
                timestamp,
                message,
                type
            };
            diagnosticLog.push(entry);
            
            console.log(`[${timestamp}] ${message}`);
            updateRealTimeLog();
        }
        
        function updateRealTimeLog() {
            const logDiv = document.getElementById('realTimeLog');
            logDiv.innerHTML = diagnosticLog.map(entry => 
                `<div class="log-entry log-${entry.type}">[${entry.timestamp}] ${entry.message}</div>`
            ).join('');
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            diagnosticLog = [];
            updateRealTimeLog();
        }
        
        function updateSection(sectionId, content, className = '') {
            const section = document.getElementById(sectionId);
            section.innerHTML = content;
            if (className) {
                section.className = className;
            }
        }
        
        // 环境检查
        function checkEnvironment() {
            const checks = [];
            
            // 浏览器检查
            checks.push(`浏览器: ${navigator.userAgent}`);
            
            // MediaSource API检查
            if (window.MediaSource) {
                checks.push(`✅ MediaSource API: 支持`);
                
                // 编解码器检查
                const codecs = [
                    'audio/mpeg',
                    'audio/webm; codecs="opus"',
                    'audio/mp4; codecs="mp4a.40.2"'
                ];
                
                codecs.forEach(codec => {
                    const supported = MediaSource.isTypeSupported(codec);
                    checks.push(`${supported ? '✅' : '❌'} 编解码器 ${codec}: ${supported ? '支持' : '不支持'}`);
                });
            } else {
                checks.push(`❌ MediaSource API: 不支持`);
            }
            
            // WebSocket检查
            if (window.WebSocket) {
                checks.push(`✅ WebSocket API: 支持`);
            } else {
                checks.push(`❌ WebSocket API: 不支持`);
            }
            
            // AudioContext检查
            if (window.AudioContext || window.webkitAudioContext) {
                checks.push(`✅ Web Audio API: 支持`);
            } else {
                checks.push(`❌ Web Audio API: 不支持`);
            }
            
            updateSection('environmentCheck', checks.join('<br>'));
            log('环境检查完成');
        }
        
        // 静态文件检查
        async function checkStaticFiles() {
            const checks = [];
            
            // 检查WebAudioPlayer类是否可用
            if (typeof WebAudioPlayer !== 'undefined') {
                checks.push('✅ WebAudioPlayer类: 已加载');
                log('WebAudioPlayer类加载成功', 'success');
            } else {
                checks.push('❌ WebAudioPlayer类: 未加载');
                log('WebAudioPlayer类加载失败', 'error');
            }
            
            // 检查静态文件URL
            const moduleFiles = [
                '/static/js/connection.js',
                '/static/js/audio_player.js',
                '/static/js/protocol.js',
                '/static/js/web_audio_player_v2.js',
                '/static/js/player_loader.js'
            ];
            
            for (const file of moduleFiles) {
                try {
                    const response = await fetch(file);
                    const filename = file.split('/').pop();
                    if (response.ok) {
                        checks.push(`✅ ${filename}: 可访问`);
                        log(`静态文件${filename}可访问`, 'success');
                    } else {
                        checks.push(`❌ ${filename}: HTTP ${response.status}`);
                        log(`静态文件${filename}访问失败: ${response.status}`, 'error');
                    }
                } catch (error) {
                    const filename = file.split('/').pop();
                    checks.push(`❌ ${filename}: ${error.message}`);
                    log(`静态文件检查错误: ${error.message}`, 'error');
                }
            }
            
            updateSection('staticFileCheck', checks.join('<br>'));
        }
        
        // WebAudioPlayer初始化测试
        async function testPlayerInitialization() {
            log('开始WebAudioPlayer初始化测试');
            const checks = [];
            
            try {
                if (typeof WebAudioPlayer === 'undefined') {
                    checks.push('❌ WebAudioPlayer类不可用');
                    log('WebAudioPlayer类不可用', 'error');
                    updateSection('playerInitCheck', checks.join('<br>'), 'error');
                    return false;
                }
                
                log('创建WebAudioPlayer实例...');
                testPlayer = new WebAudioPlayer();
                checks.push('✅ WebAudioPlayer实例创建成功');
                
                log('等待初始化完成...');
                const initSuccess = await testPlayer.waitForInitialization();
                
                if (initSuccess) {
                    checks.push('✅ WebAudioPlayer初始化成功');
                    checks.push(`✅ 是否已初始化: ${testPlayer.isInitialized}`);
                    checks.push(`✅ 是否已连接: ${testPlayer.isConnected}`);
                    log('WebAudioPlayer初始化成功', 'success');
                } else {
                    checks.push('❌ WebAudioPlayer初始化失败');
                    checks.push(`❌ 错误信息: ${testPlayer.initializationError || '未知错误'}`);
                    log('WebAudioPlayer初始化失败', 'error');
                }
                
                updateSection('playerInitCheck', checks.join('<br>'), initSuccess ? 'success' : 'error');
                return initSuccess;
                
            } catch (error) {
                checks.push(`❌ 初始化异常: ${error.message}`);
                log(`WebAudioPlayer初始化异常: ${error.message}`, 'error');
                updateSection('playerInitCheck', checks.join('<br>'), 'error');
                return false;
            }
        }
        
        // WebSocket连接测试
        async function testWebSocketConnection() {
            log('开始WebSocket连接测试');
            const checks = [];
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            // V2 WebSocket endpoint with client ID
            const clientId = 'diagnostic-' + Date.now();
            const wsUrl = `${protocol}//${window.location.host}/ws/v2/stream?client_id=${clientId}`;
            
            checks.push(`🔗 连接URL: ${wsUrl}`);
            log(`尝试连接到: ${wsUrl}`);
            
            return new Promise((resolve) => {
                const testWs = new WebSocket(wsUrl);
                const timeout = setTimeout(() => {
                    checks.push('❌ 连接超时 (5秒)');
                    log('WebSocket连接超时', 'error');
                    updateSection('websocketCheck', checks.join('<br>'), 'error');
                    testWs.close();
                    resolve(false);
                }, 5000);
                
                testWs.onopen = () => {
                    clearTimeout(timeout);
                    checks.push('✅ WebSocket连接成功');
                    log('WebSocket连接成功', 'success');
                    
                    // 发送测试消息
                    const testMsg = JSON.stringify({
                        type: 'ping',
                        timestamp: Date.now()
                    });
                    testWs.send(testMsg);
                    checks.push('✅ 测试消息已发送');
                    log('测试消息已发送', 'info');
                };
                
                testWs.onmessage = (event) => {
                    checks.push(`✅ 收到响应: ${event.data}`);
                    log(`收到WebSocket响应: ${event.data}`, 'success');
                    updateSection('websocketCheck', checks.join('<br>'), 'success');
                    testWs.close();
                    resolve(true);
                };
                
                testWs.onerror = (error) => {
                    clearTimeout(timeout);
                    checks.push(`❌ 连接错误: ${error}`);
                    log(`WebSocket连接错误: ${error}`, 'error');
                    updateSection('websocketCheck', checks.join('<br>'), 'error');
                    resolve(false);
                };
                
                testWs.onclose = (event) => {
                    if (!event.wasClean) {
                        checks.push(`⚠️ 连接意外关闭: Code ${event.code}, Reason: ${event.reason}`);
                        log(`WebSocket意外关闭: ${event.code} - ${event.reason}`, 'warning');
                    }
                };
            });
        }
        
        // 模拟控制面板行为
        async function simulateControlPanelBehavior() {
            log('开始模拟控制面板行为');
            
            // 模拟live_control_panel.html中的初始化过程
            try {
                log('模拟: 检查WebAudioPlayer类...');
                if (typeof WebAudioPlayer === 'undefined') {
                    log('❌ 模拟失败: WebAudioPlayer不可用', 'error');
                    return;
                }
                
                log('模拟: 创建WebAudioPlayer实例...');
                const mockPlayer = new WebAudioPlayer();
                
                log('模拟: 等待初始化完成...');
                const initSuccess = await mockPlayer.waitForInitialization();
                
                if (initSuccess) {
                    log('✅ 模拟成功: 控制面板应该显示已连接状态', 'success');
                    
                    // 检查连接状态
                    if (mockPlayer.isConnected) {
                        log('✅ 模拟成功: 音频流已连接，启动按钮应该可用', 'success');
                    } else {
                        log('⚠️ 模拟警告: 音频流未连接，启动按钮不可用', 'warning');
                    }
                } else {
                    log('❌ 模拟失败: 初始化未完成', 'error');
                }
                
            } catch (error) {
                log(`❌ 模拟异常: ${error.message}`, 'error');
            }
        }
        
        // 运行完整诊断
        async function runFullDiagnostic() {
            log('=== 开始完整诊断 ===', 'info');
            clearLog();
            
            // 步骤1: 环境检查
            log('步骤1: 环境检查');
            checkEnvironment();
            
            // 步骤2: 静态文件检查
            log('步骤2: 静态文件检查');
            await checkStaticFiles();
            
            // 步骤3: WebAudioPlayer初始化
            log('步骤3: WebAudioPlayer初始化测试');
            const playerInit = await testPlayerInitialization();
            
            // 步骤4: WebSocket连接
            log('步骤4: WebSocket连接测试');
            const wsConnection = await testWebSocketConnection();
            
            // 步骤5: 控制面板模拟
            log('步骤5: 控制面板行为模拟');
            await simulateControlPanelBehavior();
            
            // 诊断结果
            log('=== 诊断完成 ===', 'info');
            if (playerInit && wsConnection) {
                log('🎉 诊断结果: 所有关键功能正常，WebAudioPlayer应该可用', 'success');
            } else {
                log('💥 诊断结果: 发现问题，请检查上述错误', 'error');
            }
        }
        
        // 页面加载时自动运行基本检查
        window.addEventListener('load', () => {
            log('页面加载完成，开始基本检查');
            checkEnvironment();
            checkStaticFiles();
        });
        
        // 捕获全局错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error.message}`, 'error');
        });
        
        // 捕获Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise错误: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>