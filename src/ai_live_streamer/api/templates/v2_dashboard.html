<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播系统 V2 - 控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            border-radius: 10px;
            padding: 20px 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            font-size: 28px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .version-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            color: #333;
            font-size: 20px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .status-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #667eea;
        }
        
        .status-item .label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .status-item .value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .status-online {
            color: #22c55e;
        }
        
        .status-offline {
            color: #ef4444;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 10px;
            background: #f8f9fa;
            margin-bottom: 8px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list li::before {
            content: "✨";
        }
        
        .client-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .client-item {
            background: #f8f9fa;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .client-id {
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
        
        .client-status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-healthy {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-at-risk {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-depleted {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #e5e7eb;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #d1d5db;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                🚀 AI直播系统控制台
                <span class="version-badge">V2.0</span>
            </h1>
        </div>
        
        <div class="main-grid">
            <!-- 系统状态 -->
            <div class="card">
                <h2>📊 系统状态</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="label">服务状态</div>
                        <div class="value status-online" id="service-status">在线</div>
                    </div>
                    <div class="status-item">
                        <div class="label">WebSocket V2</div>
                        <div class="value" id="ws-status">未连接</div>
                    </div>
                    <div class="status-item">
                        <div class="label">播放列表模式</div>
                        <div class="value" id="playlist-mode">智能模式</div>
                    </div>
                    <div class="status-item">
                        <div class="label">QA插入策略</div>
                        <div class="value" id="qa-strategy">MIN策略</div>
                    </div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="cache-hits">0</div>
                        <div class="metric-label">缓存命中</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="synthesis-queue">0</div>
                        <div class="metric-label">合成队列</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="active-clients">0</div>
                        <div class="metric-label">活跃客户端</div>
                    </div>
                </div>
            </div>
            
            <!-- V2新功能 -->
            <div class="card">
                <h2>✨ V2架构特性</h2>
                <ul class="feature-list">
                    <li>服务器端智能播放列表管理</li>
                    <li>WebSocket V2双向通信协议</li>
                    <li>MIN策略智能QA插入</li>
                    <li>主动内容预合成</li>
                    <li>客户端缓冲区健康监控</li>
                    <li>状态恢复与快照管理</li>
                    <li>Future-based缓存防止踩踏</li>
                    <li>实时系统性能监控</li>
                </ul>
            </div>
            
            <!-- 客户端连接 -->
            <div class="card">
                <h2>🔗 客户端连接</h2>
                <div class="client-list" id="client-list">
                    <div class="client-item">
                        <span class="client-id">等待客户端连接...</span>
                    </div>
                </div>
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="connectWebSocket()">连接WebSocket</button>
                    <button class="btn btn-secondary" onclick="refreshClients()">刷新列表</button>
                </div>
            </div>
            
            <!-- 播放控制 -->
            <div class="card">
                <h2>🎮 播放控制</h2>
                <!-- 状态反馈区域 -->
                <div id="status-feedback" style="padding: 10px; margin-bottom: 15px; border-radius: 5px; background: #f8f9fa; color: #666; font-size: 14px; display: none;">
                    <span id="status-icon">ℹ️</span>
                    <span id="status-message">准备就绪</span>
                </div>
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="startStream()">开始直播</button>
                    <button class="btn btn-secondary" onclick="pauseStream()">暂停</button>
                    <button class="btn btn-secondary" onclick="resumeStream()">恢复</button>
                    <button class="btn btn-secondary" onclick="stopStream()">停止</button>
                </div>
                
                <div style="margin-top: 20px;">
                    <h3 style="font-size: 16px; margin-bottom: 10px;">插入QA测试</h3>
                    <input type="text" id="qa-input" placeholder="输入测试问题..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px;">
                    <button class="btn btn-primary" onclick="insertQA()">插入QA</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Session Manager - 统一的会话状态管理 -->
    <script src="/static/js/session_manager.js?v=1.0.0"></script>
    
    <script>
        // 封装所有流相关的状态到单一对象 (v2DashboardState 避免命名冲突)
        const v2DashboardState = {
            sessionId: null,
            clientId: 'v2-dashboard-' + Date.now(),
            ws: null,
            isConnected: false,
            isConnecting: false,
            isStarting: false
        };
        
        // UI状态管理
        const uiState = {
            statusFeedback: null,
            startButton: null,
            connectButton: null,
            pauseButton: null,
            resumeButton: null,
            stopButton: null
        };
        
        // 更新状态反馈UI
        function updateStatusFeedback(message, type = 'info') {
            const feedbackEl = document.getElementById('status-feedback');
            const iconEl = document.getElementById('status-icon');
            const messageEl = document.getElementById('status-message');
            
            if (!feedbackEl) return;
            
            feedbackEl.style.display = 'block';
            messageEl.textContent = message;
            
            // 根据类型设置样式和图标
            switch(type) {
                case 'success':
                    feedbackEl.style.background = '#dcfce7';
                    feedbackEl.style.color = '#166534';
                    iconEl.textContent = '✅';
                    break;
                case 'error':
                    feedbackEl.style.background = '#fee2e2';
                    feedbackEl.style.color = '#991b1b';
                    iconEl.textContent = '❌';
                    break;
                case 'warning':
                    feedbackEl.style.background = '#fef3c7';
                    feedbackEl.style.color = '#92400e';
                    iconEl.textContent = '⚠️';
                    break;
                case 'loading':
                    feedbackEl.style.background = '#e0f2fe';
                    feedbackEl.style.color = '#075985';
                    iconEl.textContent = '⏳';
                    break;
                default:
                    feedbackEl.style.background = '#f8f9fa';
                    feedbackEl.style.color = '#666';
                    iconEl.textContent = 'ℹ️';
            }
        }
        
        // 更新按钮状态
        function setButtonState(buttonId, enabled) {
            const button = document.querySelector(`button[onclick*="${buttonId}"]`);
            if (button) {
                button.disabled = !enabled;
                if (!enabled) {
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                } else {
                    button.style.opacity = '1';
                    button.style.cursor = 'pointer';
                }
            }
        }
        
        function connectWebSocket() {
            // 检查是否已连接
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                console.log('WebSocket already connected');
                updateStatusFeedback('WebSocket 已连接', 'info');
                return;
            }
            
            // 检查是否正在连接
            if (v2DashboardState.isConnecting) {
                console.log('WebSocket connection already in progress');
                return;
            }
            
            // 验证 session ID
            if (!v2DashboardState.sessionId) {
                console.error('No session ID available. Please start stream first.');
                updateStatusFeedback('请先点击"开始直播"按钮', 'warning');
                return;
            }
            
            v2DashboardState.isConnecting = true;
            updateStatusFeedback('正在连接 WebSocket...', 'loading');
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/v2/stream?client_id=${v2DashboardState.clientId}&session_id=${v2DashboardState.sessionId}`;
            
            console.log('Connecting to:', wsUrl);
            
            v2DashboardState.ws = new WebSocket(wsUrl);
            
            v2DashboardState.ws.onopen = () => {
                console.log('WebSocket V2 connected');
                v2DashboardState.isConnected = true;
                v2DashboardState.isConnecting = false;
                document.getElementById('ws-status').textContent = '已连接';
                document.getElementById('ws-status').className = 'value status-online';
                updateStatusFeedback('WebSocket 连接成功', 'success');
                setButtonState('connectWebSocket', false);
                setButtonState('pauseStream', true);
                setButtonState('resumeStream', true);
                setButtonState('stopStream', true);
            };
            
            v2DashboardState.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (e) {
                    console.log('Received binary data:', event.data);
                }
            };
            
            v2DashboardState.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                v2DashboardState.isConnecting = false;
                document.getElementById('ws-status').textContent = '连接错误';
                document.getElementById('ws-status').className = 'value status-offline';
                updateStatusFeedback('WebSocket 连接错误', 'error');
                setButtonState('connectWebSocket', true);
                setButtonState('pauseStream', false);
                setButtonState('resumeStream', false);
                setButtonState('stopStream', false);
            };
            
            v2DashboardState.ws.onclose = () => {
                console.log('WebSocket closed');
                v2DashboardState.isConnected = false;
                v2DashboardState.isConnecting = false;
                v2DashboardState.ws = null;
                document.getElementById('ws-status').textContent = '未连接';
                document.getElementById('ws-status').className = 'value status-offline';
                updateStatusFeedback('WebSocket 已断开', 'info');
                setButtonState('connectWebSocket', true);
                setButtonState('pauseStream', false);
                setButtonState('resumeStream', false);
                setButtonState('stopStream', false);
            };
        }
        
        function handleMessage(data) {
            switch(data.type) {
                case 'status':
                    updateStatus(data);
                    break;
                case 'clients':
                    updateClientList(data.clients);
                    break;
                case 'metrics':
                    updateMetrics(data);
                    break;
            }
        }
        
        function updateStatus(data) {
            if (data.playlist_mode) {
                document.getElementById('playlist-mode').textContent = data.playlist_mode;
            }
            if (data.qa_strategy) {
                document.getElementById('qa-strategy').textContent = data.qa_strategy;
            }
        }
        
        function updateClientList(clients) {
            const listEl = document.getElementById('client-list');
            listEl.innerHTML = '';
            
            if (!clients || clients.length === 0) {
                listEl.innerHTML = '<div class="client-item"><span class="client-id">暂无客户端连接</span></div>';
                return;
            }
            
            clients.forEach(client => {
                const item = document.createElement('div');
                item.className = 'client-item';
                
                const statusClass = client.buffer_health === 'HEALTHY' ? 'status-healthy' :
                                  client.buffer_health === 'AT_RISK' ? 'status-at-risk' :
                                  'status-depleted';
                
                item.innerHTML = `
                    <span class="client-id">${client.id}</span>
                    <span class="client-status ${statusClass}">${client.buffer_health}</span>
                `;
                
                listEl.appendChild(item);
            });
            
            document.getElementById('active-clients').textContent = clients.length;
        }
        
        function updateMetrics(data) {
            if (data.cache_hits !== undefined) {
                document.getElementById('cache-hits').textContent = data.cache_hits;
            }
            if (data.synthesis_queue !== undefined) {
                document.getElementById('synthesis-queue').textContent = data.synthesis_queue;
            }
        }
        
        function startStream() {
            // 防止重复点击
            if (v2DashboardState.isStarting) {
                console.log('Stream start already in progress');
                return;
            }
            
            // 如果已有连接，先清理
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                console.log('Closing existing WebSocket connection');
                v2DashboardState.ws.close();
                v2DashboardState.ws = null;
                v2DashboardState.isConnected = false;
            }
            
            v2DashboardState.isStarting = true;
            setButtonState('startStream', false);
            updateStatusFeedback('正在启动直播...', 'loading');
            
            fetch('/api/control/start-stream', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    mode: 'smart',
                    topic: 'AI直播测试'
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Stream started:', data);
                v2DashboardState.sessionId = data.session_id;  // 保存 session_id
                console.log('Session ID saved:', v2DashboardState.sessionId);
                updateStatusFeedback('直播已启动，正在连接...', 'success');
                connectWebSocket();  // 自动连接 WebSocket
                v2DashboardState.isStarting = false;
                setButtonState('startStream', true);
            })
            .catch(error => {
                console.error('Error:', error);
                updateStatusFeedback(`启动失败: ${error.message}`, 'error');
                v2DashboardState.isStarting = false;
                setButtonState('startStream', true);
            });
        }
        
        function pauseStream() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'pause'}));
                updateStatusFeedback('直播已暂停', 'info');
            } else {
                updateStatusFeedback('WebSocket 未连接', 'warning');
            }
        }
        
        function resumeStream() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'resume'}));
                updateStatusFeedback('直播已恢复', 'success');
            } else {
                updateStatusFeedback('WebSocket 未连接', 'warning');
            }
        }
        
        function stopStream() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'stop'}));
                updateStatusFeedback('正在停止直播...', 'loading');
                setTimeout(() => {
                    v2DashboardState.ws.close(1000, 'User stopped stream');
                    
                    // 使用 SessionManager 清理状态
                    if (window.sessionManager) {
                        window.sessionManager.endSession('normal');
                    } else {
                        // 降级处理：手动清理
                        v2DashboardState.sessionId = null;
                        sessionStorage.removeItem('currentSessionId');
                    }
                    
                    updateStatusFeedback('直播已停止', 'info');
                }, 500);
            } else {
                updateStatusFeedback('WebSocket 未连接', 'warning');
            }
        }
        
        function insertQA() {
            const question = document.getElementById('qa-input').value;
            if (!question) return;
            
            fetch('/api/control/submit-question', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    text: question,
                    priority: 'HIGH'
                })
            }).then(response => response.json())
              .then(data => {
                  console.log('QA inserted:', data);
                  document.getElementById('qa-input').value = '';
              })
              .catch(error => console.error('Error:', error));
        }
        
        function refreshClients() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'get_clients'}));
            } else {
                updateStatusFeedback('请先连接 WebSocket', 'warning');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            // 初始化按钮状态
            setButtonState('startStream', true);
            setButtonState('connectWebSocket', false);
            setButtonState('pauseStream', false);
            setButtonState('resumeStream', false);
            setButtonState('stopStream', false);
            
            // 显示初始状态
            updateStatusFeedback('准备就绪，请点击"开始直播"', 'info');
        });
    </script>
</body>
</html>