        // 状态管理
        let streamState = {
            status: 'stopped',
            questions: [],
            startTime: null,
            durationMetrics: null,
            onBreak: false,
            breakUntil: null,
            scriptSegments: [],
            currentScriptLoaded: false,
            sourceFormId: null,
            // 会话管理 - 修复QA提交问题
            sessionId: null,  // 存储当前的会话ID
            // 句子级别状态
            currentSentenceIndex: 0,
            currentSentenceText: '',
            segmentSentences: [],
            totalSentencesInSegment: 0,
            contentExpanded: false,
            // 开发测试标志
            enableDemoMode: false,  // 暂时禁用演示模式，查看真实数据
            // 音频流状态
            audioStreaming: {
                isEnabled: false,
                isConnected: false,
                isPlaying: false,
                status: 'disconnected',
                // Q&A音频状态
                qaAudioActive: false,
                liveAudioPaused: false
            },
            // 🚀 QA提交状态管理 - 防抖和乐观UI
            qaSubmission: {
                isSubmitting: false,
                submittedAt: null,
                submittedQuestions: new Map(), // 存储已提交的问题
                cooldownMs: 3000, // 3秒冷却时间
                optimisticQuestions: [] // 乐观UI显示的问题列表
            }
        };

        // 全局音频播放器实例
        let webAudioPlayer = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔍 Page DOMContentLoaded - starting initialization...');
            
            // 注册 SessionManager 监听器
            if (window.sessionManager) {
                console.log('📡 Registering SessionManager listener...');
                window.sessionManager.addListener((event, data) => {
                    console.log('📡 Session event:', event, data);
                    
                    switch(event) {
                        case 'session-started':
                            streamState.sessionId = data.sessionId;
                            console.log('✅ Session started:', data.sessionId);
                            break;
                            
                        case 'session-active':
                            console.log('✅ Session activated:', data.sessionId);
                            break;
                            
                        case 'session-ended':
                            streamState.sessionId = null;
                            console.log('🔚 Session ended:', data.reason);
                            resetUIToInitialState();
                            break;
                    }
                });
                
                // 页面加载时清理任何过期状态
                const state = window.sessionManager.getState();
                if (!state.isActive) {
                    console.log('🧹 Cleaning up expired session state...');
                    window.sessionManager.endSession('cleanup');
                }
            } else {
                // 降级处理：如果 SessionManager 不可用，使用原来的清理逻辑
                console.log('⚠️ SessionManager not available, using fallback cleanup');
                sessionStorage.removeItem('currentSessionId');
                streamState.sessionId = null;
            }
            
            console.log('✅ Session状态已重置，确保从干净状态开始');
            
            // 重置UI到初始"无会话"状态
            resetUIToInitialState();
            
            try {
                // 设置默认的播放内容信息
                updateCurrentContentInfo({
                    segment_title: '',
                    content_preview: '',
                    is_playing_audio: false,
                    audio_playback_info: {}
                });
                
                // 初始化音频流播放器 (异步) - 使用try-catch确保错误不会阻止页面功能
                console.log('🔍 Initializing audio streaming...');
                try {
                    await initializeAudioStreaming();
                    console.log('✅ Audio streaming initialization completed');
                } catch (audioError) {
                    console.error('❌ Audio streaming initialization failed:', audioError);
                    // 显示用户友好的错误信息，但不阻止页面其他功能
                    const audioStreamStatus = document.getElementById('audioStreamStatus');
                    if (audioStreamStatus) {
                        audioStreamStatus.textContent = '音频初始化失败 - 页面其他功能仍可使用';
                        audioStreamStatus.className = 'status error';
                    }
                }
                
                // 页面加载时就尝试加载可用脚本 - 独立于音频初始化
                try {
                    loadAvailableScripts();
                    console.log('✅ Available scripts loading initiated');
                } catch (scriptsError) {
                    console.error('❌ Error loading available scripts:', scriptsError);
                }
                
            } catch (initError) {
                console.error('❌ Critical error during page initialization:', initError);
                // 即使初始化失败，也要确保基本的页面功能可用
                showNotification('页面初始化遇到问题，部分功能可能不可用', 'warning');
            }
            
            // WebSocket架构重构：移除HTTP轮询，改为WebSocket状态同步
            // 初始状态将通过WebSocket连接后自动获取
            console.log('🔄 Using WebSocket for state synchronization');
            
            updateUI();
            // startStatusPolling(); // 已废弃：改用WebSocket实时推送
            
            console.log('✅ Page initialization completed');
        });
        
        // 加载可用的脚本预览
        async function loadAvailableScripts() {
            try {
                // 首先尝试加载已知的脚本ID
                await loadSpecificScript('a8ceb6d7-a9a1-40e2-8bec-b08f65972426');
            } catch (error) {
                console.log('加载已知脚本失败，尝试获取脚本列表:', error);
                
                try {
                    // 获取所有可用的脚本预览
                    const response = await fetch('/api/script-preview/list');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.previews && data.previews.length > 0) {
                            // 使用最新的脚本
                            const latestScript = data.previews[0];
                            await loadSpecificScript(latestScript.form_id);
                            console.log('加载最新脚本:', latestScript.form_id);
                        } else {
                            console.log('没有可用的脚本预览');
                        }
                    }
                } catch (listError) {
                    console.log('无法获取脚本列表:', listError);
                }
            }
        }
        
        // 加载指定的脚本
        async function loadSpecificScript(scriptId) {
            if (!scriptId) return;
            
            try {
                const response = await fetch(`/api/script-preview/timeline/${scriptId}`);
                if (response.ok) {
                    const data = await response.json();
                    streamState.scriptSegments = data.timeline.segments;
                    streamState.currentScriptLoaded = true;
                    streamState.sourceFormId = scriptId;
                    renderScriptTimeline(data.timeline.segments);
                    console.log('成功加载脚本:', scriptId);
                } else {
                    throw new Error(`API返回错误: ${response.status}`);
                }
            } catch (error) {
                console.error('加载脚本失败:', scriptId, error);
                throw error;
            }
        }
        
        // 切换脚本预览面板
        function toggleScriptPreview() {
            const header = document.querySelector('.collapsible-header');
            const content = document.getElementById('scriptTimelineContent');
            
            header.classList.toggle('expanded');
            content.classList.toggle('expanded');
            
            // 如果展开且还未加载脚本，尝试重新加载
            if (header.classList.contains('expanded') && !streamState.currentScriptLoaded) {
                loadAvailableScripts();
            }
        }
        
        // 加载脚本时间线（已弃用，由loadAvailableScripts取代）
        async function loadScriptTimeline() {
            // 这个函数现在只作为fallback，主要逻辑已转移到loadAvailableScripts
            console.log('loadScriptTimeline被调用，尝试重新加载脚本');
            await loadAvailableScripts();
        }
        
        // 基于plan_outline生成脚本段落
        function generateSegmentsFromPlanOutline() {
            const segments = [];
            
            // 为每个段落创建一个脚本项
            for (let i = 0; i < streamState.totalSegments; i++) {
                // 默认标题，后续可以通过API获取真实的plan_outline更新
                const outlineTitle = `段落 ${i + 1}`;
                
                segments.push({
                    segment_id: `segment_${i + 1}`,
                    type: i === 0 ? 'opening' : 
                          i === streamState.totalSegments - 1 ? 'closing' : 
                          i % 3 === 1 ? 'selling_point' : 'interaction',
                    title: outlineTitle,
                    content: `这是${outlineTitle}的内容。直播进行中，具体内容将根据实际情况动态生成。`,
                    estimated_duration_seconds: 180 + (i * 30), // 3-8分钟不等
                    priority: 'medium'
                });
            }
            
            return segments;
        }
        
        // 更新脚本段落的真实标题（从状态API获取）
        function updateSegmentTitlesFromStatus(segments) {
            // 这个函数会在状态更新时调用，用真实的plan_outline更新段落标题
            // 暂时返回原segments，后续可以扩展
            return segments;
        }
        
        // 更新当前播放段落的高亮显示
        function updateCurrentSegmentHighlight() {
            if (!streamState.currentScriptLoaded) return;
            
            // 移除所有current类
            document.querySelectorAll('.script-segment').forEach(el => el.classList.remove('current'));
            
            // 添加current类到当前段落
            if (streamState.currentSegment > 0) {
                const currentIndex = streamState.currentSegment - 1;
                const segments = document.querySelectorAll('.script-segment');
                if (segments[currentIndex]) {
                    segments[currentIndex].classList.add('current');
                    segments[currentIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        }
        
        // 渲染脚本时间线
        function renderScriptTimeline(segments) {
            const timeline = document.getElementById('scriptTimeline');
            
            if (!segments || segments.length === 0) {
                timeline.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">暂无脚本内容</div>';
                return;
            }
            
            let html = '';
            segments.forEach((segment, index) => {
                const isCurrent = index === streamState.currentSegment - 1;
                const segmentClass = isCurrent ? 'script-segment current' : 'script-segment';
                
                html += `
                    <div class="${segmentClass}" onclick="jumpToSegment(${index + 1})">
                        <div class="segment-header">
                            <span class="segment-title">${segment.title}</span>
                            <span class="segment-duration">${segment.estimated_duration_seconds}秒</span>
                        </div>
                        <div class="segment-content-preview">${segment.content}</div>
                        <div class="segment-meta">
                            <span class="segment-type">${segment.type}</span>
                            <span>优先级: ${segment.priority}</span>
                            <span>段落 ${index + 1}/${segments.length}</span>
                        </div>
                    </div>
                `;
            });
            
            timeline.innerHTML = html;
            
            // 滚动到当前段落
            if (streamState.currentSegment > 0) {
                const currentElement = timeline.querySelector('.script-segment.current');
                if (currentElement) {
                    currentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        }
        
        // 点击段落时仅高亮显示
        function jumpToSegment(segmentNumber) {
            // 移除所有current类
            document.querySelectorAll('.script-segment').forEach(el => el.classList.remove('current'));
            
            // 添加current类到点击的段落
            const segments = document.querySelectorAll('.script-segment');
            if (segments[segmentNumber - 1]) {
                segments[segmentNumber - 1].classList.add('current');
                segments[segmentNumber - 1].scrollIntoView({ behavior: 'smooth', block: 'center' });
                showNotification(`查看段落 ${segmentNumber}`, 'info');
            }
        }

        // WebSocket架构重构：HTTP轮询函数已废弃
        // 处理服务端推送的状态更新（通过WebSocket）
        function handleServerStateUpdate(stateData) {
            try {
                console.log('📨 Received server state update via WebSocket');
                updateStreamState(stateData);
                return stateData;
            } catch (error) {
                console.error('Error processing server state update:', error);
                updateConnectionStatus(false);
                throw error;
            }
        }

        // 重置UI到初始"无会话"状态
        function resetUIToInitialState() {
            console.log('🔄 重置UI到初始状态...');
            
            // 重置streamState中与session相关的状态
            streamState.sessionId = null;
            streamState.status = 'stopped';
            streamState.questions = [];
            streamState.startTime = null;
            streamState.onBreak = false;
            streamState.breakUntil = null;
            streamState.currentScriptLoaded = false;
            
            // 🔧 关键修复：清除WebAudioPlayer中的session ID
            // WebAudioPlayerV2 不再需要clearSession
            // 清理逻辑已内置在stop()方法中
            
            // 重置音频流状态
            streamState.audioStreaming.isConnected = false;
            streamState.audioStreaming.isPlaying = false;
            streamState.audioStreaming.status = 'disconnected';
            streamState.audioStreaming.qaAudioActive = false;
            streamState.audioStreaming.liveAudioPaused = false;
            
            // 重置QA状态
            streamState.qaSubmission.isSubmitting = false;
            streamState.qaSubmission.submittedAt = null;
            streamState.qaSubmission.submittedQuestions.clear();
            streamState.qaSubmission.optimisticQuestions = [];
            
            // 更新UI显示
            updateAudioStreamStatus('disconnected', '音频系统未连接');
            updateConnectionStatus(false);
            
            console.log('✅ UI状态已重置到初始状态');
        }

        // WebSocket架构重构：状态轮询已废弃
        // 状态更新现在通过WebSocket实时推送
        // function startStatusPolling() - DEPRECATED

        // 更新流状态
        function updateStreamState(data) {
            updateConnectionStatus(true);
            
            if (data.status) {
                streamState.status = data.status;
                streamState.questions = data.question_queue_length || 0;
                
                // Update duration metrics if available
                if (data.duration_metrics) {
                    streamState.durationMetrics = data.duration_metrics;
                }
                
                // Update break status
                streamState.onBreak = data.on_break || false;
                streamState.breakUntil = data.break_until || null;
                
                // Update stream source info if available
                if (data.time_context) {
                    streamState.sourceFormId = data.time_context.source_form_id;
                    updateStreamSourceInfo(data.time_context, data.playback_status);
                }
                
                // 只在有直播时更新播放进度，脚本加载由其他逻辑处理
                if (streamState.currentScriptLoaded && streamState.totalSegments > 0) {
                    updateCurrentSegmentHighlight();
                }
                
                // Update current content info if available
                if (data.current_content) {
                    updateCurrentContentInfo(data.current_content);
                }
            }
            
            updateUI();
        }

        // Update stream source information display
        function updateStreamSourceInfo(timeContext, playbackStatus) {
            const sourceInfo = document.getElementById('streamSourceInfo');
            
            if (timeContext.source_form_id || timeContext.stream_title || timeContext.product_name || timeContext.company_name) {
                sourceInfo.style.display = 'block';
                
                document.getElementById('streamTitle').textContent = timeContext.stream_title || (timeContext.source_form_id ? `脚本 ${timeContext.source_form_id}` : '--');
                document.getElementById('productName').textContent = timeContext.product_name || '--';
                document.getElementById('companyName').textContent = timeContext.company_name || '--';
                document.getElementById('sourceFormId').textContent = timeContext.source_form_id || '--';
                document.getElementById('scriptSegmentsCount').textContent = timeContext.script_segments_count || '--';
                
                // Update playback status with detailed information
                const statusDetails = document.getElementById('playbackStatusDetails');
                const statusMessage = document.getElementById('playbackStatusMessage');
                const statusWarning = document.getElementById('playbackWarning');
                
                if (playbackStatus) {
                    if (playbackStatus.content_playback_started) {
                        document.getElementById('contentPlaybackStatus').textContent = '✅ 正常播放';
                        document.getElementById('contentPlaybackStatus').style.color = '#27ae60';
                        statusDetails.style.display = 'none';
                    } else {
                        document.getElementById('contentPlaybackStatus').textContent = '⚠️ 播放异常';
                        document.getElementById('contentPlaybackStatus').style.color = '#f39c12';
                        
                        // Show detailed status
                        statusDetails.style.display = 'block';
                        statusMessage.textContent = '脚本播放可能存在问题';
                        statusWarning.textContent = playbackStatus.warning || '未知播放问题';
                    }
                } else {
                    document.getElementById('contentPlaybackStatus').textContent = '🔄 准备中';
                    document.getElementById('contentPlaybackStatus').style.color = '#667eea';
                    statusDetails.style.display = 'none';
                }
            } else {
                sourceInfo.style.display = 'none';
            }
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (connected) {
                statusDot.classList.add('active');
                statusText.textContent = '系统在线';
            } else {
                statusDot.classList.remove('active');
                statusText.textContent = '系统离线';
            }
        }

        // Update current content information display
        function updateCurrentContentInfo(currentContent) {
            const contentInfoPanel = document.getElementById('currentContentInfo');
            
            // 面板始终可见，不再根据内容判断
            // contentInfoPanel.style.display = 'block';  // 已经移除了display:none，不需要这行
            
            document.getElementById('currentSegmentTitle').textContent = currentContent.segment_title || '等待开始播放...';
            
            // 更新句子级别显示
            console.log('Current content data:', currentContent); // 调试信息
            
            if (currentContent.segment_sentences && currentContent.segment_sentences.length > 0) {
                console.log('Using sentence display with', currentContent.segment_sentences.length, 'sentences');
                updateSentenceDisplay(currentContent);
            } else {
                console.log('Fallback to simple preview, segment_sentences:', currentContent.segment_sentences);
                
                // 添加测试数据，当处于某些状态时提供演示句子
                if (streamState.enableDemoMode || 
                    (currentContent.segment_title && currentContent.segment_title !== '等待开始播放...' && currentContent.segment_title !== '--') || 
                    streamState.status === 'running' || streamState.status === 'narrating' || streamState.status === 'planning') {
                    console.log('Creating demo sentences for segment:', currentContent.segment_title);
                    // 创建演示句子数据
                    const demoContent = {
                        ...currentContent,
                        segment_sentences: [
                            '欢迎大家来到我们的直播间！',
                            '今天我们将为大家介绍一款非常优秀的产品。',
                            '这款产品具有很多独特的功能和优势。',
                            '现在让我们来详细了解一下吧。'
                        ],
                        current_sentence_index: Math.floor(Date.now() / 3000) % 4, // 每3秒切换一句
                        current_sentence_text: '',
                        total_sentences_in_segment: 4
                    };
                    demoContent.current_sentence_text = demoContent.segment_sentences[demoContent.current_sentence_index];
                    console.log('Using demo content:', demoContent);
                    updateSentenceDisplay(demoContent);
                } else {
                    // Fallback to simple preview
                    document.getElementById('currentContentPreview').textContent = currentContent.content_preview || '直播即将开始，请稍候...';
                    document.getElementById('sentenceScrollContainer').style.display = 'none';
                    document.getElementById('currentContentPreview').style.display = 'block';
                }
            }
            
            // Update audio playback status
            let audioStatus = '--';
            if (currentContent.is_playing_audio && !currentContent.is_audio_paused) {
                audioStatus = '🎵 播放中';
            } else if (currentContent.is_playing_audio && currentContent.is_audio_paused) {
                audioStatus = '⏸️ 已暂停';
            } else {
                audioStatus = '⏹️ 已停止';
            }
            
            document.getElementById('audioPlaybackStatus').textContent = audioStatus;
            
            // Update additional audio info
            const audioInfo = currentContent.audio_playback_info || {};
            document.getElementById('audioEngineType').textContent = audioInfo.engine_type || '--';
            
            if (audioInfo.progress_percentage !== undefined) {
                document.getElementById('audioProgress').textContent = audioInfo.progress_percentage.toFixed(1) + '%';
            } else {
                document.getElementById('audioProgress').textContent = '--';
            }
        }
        
        // Update sentence-level display
        function updateSentenceDisplay(currentContent) {
            try {
                // Validate input data
                if (!currentContent || typeof currentContent !== 'object') {
                    throw new Error('Invalid current content data');
                }
                
                // Show sentence container, hide simple preview
                document.getElementById('sentenceScrollContainer').style.display = 'block';
                document.getElementById('currentContentPreview').style.display = 'none';
                
                // Update state with validation
                streamState.currentSentenceIndex = Math.max(0, parseInt(currentContent.current_sentence_index) || 0);
                streamState.currentSentenceText = (currentContent.current_sentence_text || '').toString();
                streamState.segmentSentences = Array.isArray(currentContent.segment_sentences) ? currentContent.segment_sentences : [];
                streamState.totalSentencesInSegment = Math.max(0, parseInt(currentContent.total_sentences_in_segment) || 0);
            
                // Update current sentence display
                const currentSentenceText = document.getElementById('currentSentenceText');
                const sentenceProgress = document.getElementById('sentenceProgress');
                
                if (!currentSentenceText || !sentenceProgress) {
                    console.warn('Required DOM elements not found');
                    return;
                }
                
                if (streamState.currentSentenceText) {
                    currentSentenceText.textContent = streamState.currentSentenceText;
                    sentenceProgress.textContent = `句子 ${streamState.currentSentenceIndex + 1}/${streamState.totalSentencesInSegment}`;
                    
                    // Add highlighting animation safely
                    try {
                        currentSentenceText.classList.add('highlighting');
                        setTimeout(() => {
                            currentSentenceText.classList.remove('highlighting');
                        }, 1000);
                    } catch (e) {
                        console.warn('Animation error:', e);
                    }
                } else {
                    currentSentenceText.textContent = '等待合成...';
                    sentenceProgress.textContent = '准备中...';
                }
                
                // Update expanded content if visible
                if (streamState.contentExpanded) {
                    try {
                        updateExpandedSentencesList();
                    } catch (e) {
                        console.warn('Error updating expanded sentences list:', e);
                    }
                }
                
            } catch (error) {
                console.error('Error updating sentence display:', error);
                // Fallback to simple content preview
                document.getElementById('sentenceScrollContainer').style.display = 'none';
                document.getElementById('currentContentPreview').style.display = 'block';
                document.getElementById('currentContentPreview').textContent = currentContent.content_preview || '直播即将开始，请稍候...';
            }
        }
        
        // Toggle content expansion
        function toggleContentExpansion() {
            const expandedContainer = document.getElementById('expandedContentContainer');
            const toggleButton = document.getElementById('toggleContentExpand');
            
            streamState.contentExpanded = !streamState.contentExpanded;
            
            if (streamState.contentExpanded) {
                expandedContainer.style.display = 'block';
                toggleButton.textContent = '收起';
                updateExpandedSentencesList();
            } else {
                expandedContainer.style.display = 'none';
                toggleButton.textContent = '展开';
            }
        }
        
        // Update expanded sentences list
        function updateExpandedSentencesList() {
            const allSentencesList = document.getElementById('allSentencesList');
            
            if (!streamState.segmentSentences || streamState.segmentSentences.length === 0) {
                allSentencesList.innerHTML = '<div style="color: #7f8c8d; text-align: center; padding: 10px;">暂无句子数据</div>';
                return;
            }
            
            let html = '<div style="font-size: 13px; margin-bottom: 10px; color: #667eea;">完整段落内容:</div>';
            
            streamState.segmentSentences.forEach((sentence, index) => {
                let className = 'sentence-item';
                if (index < streamState.currentSentenceIndex) {
                    className += ' completed';
                } else if (index === streamState.currentSentenceIndex) {
                    className += ' current';
                }
                
                html += `
                    <div class="${className}" onclick="highlightSentence(${index})">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                            <span style="flex: 1;">${sentence}</span>
                            <span style="font-size: 11px; color: #7f8c8d; margin-left: 10px;">${index + 1}/${streamState.segmentSentences.length}</span>
                        </div>
                        ${index === streamState.currentSentenceIndex ? '<div class="sentence-progress-bar"><div class="sentence-progress-fill" style="width: 60%;"></div></div>' : ''}
                    </div>
                `;
            });
            
            allSentencesList.innerHTML = html;
            
            // Scroll to current sentence
            const currentSentenceElement = allSentencesList.querySelector('.sentence-item.current');
            if (currentSentenceElement) {
                currentSentenceElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
        
        // Highlight a specific sentence (for interaction)
        function highlightSentence(sentenceIndex) {
            // Visual feedback only - don't actually change the playback
            const allItems = document.querySelectorAll('.sentence-item');
            allItems.forEach((item, index) => {
                if (index === sentenceIndex) {
                    item.classList.add('highlighting');
                    setTimeout(() => {
                        item.classList.remove('highlighting');
                    }, 800);
                }
            });
            
            showNotification(`查看句子 ${sentenceIndex + 1}`, 'info');
        }
        
        // 更新UI (带防御性检查)
        function updateUI() {
            try {
            } catch (error) {
                console.error('❌ Error in updateUI:', error);
                // 不重新抛出错误，允许程序继续运行
            }
            
            // 更新脚本时间线中的当前段落高亮 (防御性检查)
            try {
                if (streamState.currentScriptLoaded && streamState.scriptSegments.length > 0) {
                    // 移除所有current类
                    const scriptSegments = document.querySelectorAll('.script-segment');
                    scriptSegments.forEach(el => el.classList.remove('current'));
                    
                    // 添加current类到当前段落
                    if (streamState.currentSegment > 0) {
                        const currentIndex = streamState.currentSegment - 1;
                        if (scriptSegments[currentIndex]) {
                            scriptSegments[currentIndex].classList.add('current');
                        }
                    }
                }
            } catch (error) {
                console.warn('⚠️ Error updating script segment highlighting:', error);
            }
            
            // 更新时长指标 (防御性检查)
            try {
                if (streamState.durationMetrics) {
                    const metrics = streamState.durationMetrics;
                    
                    // 更新时长偏差
                    const variance = metrics.schedule_variance_minutes;
                    const varianceText = variance > 0 
                        ? `+${variance.toFixed(1)}分钟` 
                        : `${variance.toFixed(1)}分钟`;
                    const varianceColor = variance > 2 ? '#e74c3c' : variance < -3 ? '#f39c12' : '#27ae60';
                    
                    
                    // 更新详细进度信息
                    const elapsedTimeElement = document.getElementById('elapsedTime');
                    if (elapsedTimeElement) {
                        elapsedTimeElement.textContent = `${metrics.elapsed_minutes.toFixed(1)}分钟`;
                    }
                    
                    const plannedDurationElement = document.getElementById('plannedDuration');
                    if (plannedDurationElement) {
                        plannedDurationElement.textContent = `${metrics.planned_duration_minutes}分钟`;
                    }
                    
                    const estimatedCompletionElement = document.getElementById('estimatedCompletion');
                    if (estimatedCompletionElement) {
                        estimatedCompletionElement.textContent = `${metrics.estimated_completion_minutes.toFixed(1)}分钟`;
                    }
                }
            } catch (error) {
                console.warn('⚠️ Error updating duration metrics:', error);
            }
            
            // 更新时长状态 (防御性检查)
            try {
                if (streamState.durationMetrics && streamState.durationMetrics.duration_status) {
                    const metrics = streamState.durationMetrics;
                    const statusMap = {
                        'on_track': '正常',
                        'ahead_of_schedule': '提前',
                        'behind_schedule': '延迟',
                        'extension_needed': '需要延长',
                        'manual_intervention': '需要干预'
                    };
                    
                    const durationStatusElement = document.getElementById('durationStatus');
                    if (durationStatusElement) {
                        durationStatusElement.textContent = statusMap[metrics.duration_status] || metrics.duration_status;
                        
                        // 状态颜色
                        const statusColor = {
                            'on_track': '#27ae60',
                            'ahead_of_schedule': '#3498db',
                            'behind_schedule': '#f39c12',
                            'extension_needed': '#e74c3c',
                            'manual_intervention': '#e74c3c'
                        };
                        durationStatusElement.style.color = statusColor[metrics.duration_status] || '#7f8c8d';
                    }
                }
            } catch (error) {
                console.warn('⚠️ Error updating duration status:', error);
            }
            
            // 重置时长指标显示 (当没有数据时) - 防御性检查
            try {
                if (!streamState.durationMetrics) {
                    
                    const elapsedTimeElement = document.getElementById('elapsedTime');
                    if (elapsedTimeElement) elapsedTimeElement.textContent = '0分钟';
                    
                    const plannedDurationElement = document.getElementById('plannedDuration');
                    if (plannedDurationElement) plannedDurationElement.textContent = '--分钟';
                    
                    const estimatedCompletionElement = document.getElementById('estimatedCompletion');
                    if (estimatedCompletionElement) estimatedCompletionElement.textContent = '--分钟';
                    
                    const durationStatusElement = document.getElementById('durationStatus');
                    if (durationStatusElement) durationStatusElement.textContent = '--';
                }
            } catch (error) {
                console.warn('⚠️ Error resetting duration metrics:', error);
            }
            
            // 更新按钮状态
            // 基本控制按钮状态更新已移除
            
            // 更新运行时间
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'stopped': '已停止',
                'planning': '已准备就绪',
                'initializing': '初始化中',
                'running': '直播中',
                'paused': '已暂停',
                'narrating': '解说中',
                'answering': '回答问题中',
                'qa_mode': '问答模式',
                'transitioning': '转换中',
                'degraded': '性能降级'
            };
            return statusMap[status] || '未知状态';
        }

        // 基本控制按钮状态更新函数已移除


        // 控制函数
        // V2架构使用服务器端智能播放列表，移除手动段落控制

        async function submitQuestion() {
            const questionText = document.getElementById('questionText').value.trim();
            const priority = document.getElementById('questionPriority').value;
            const submitButton = document.getElementById('questionSubmitBtn') || document.querySelector('button[onclick="submitQuestion()"]');
            
            if (!questionText) {
                showNotification('请输入问题内容', 'warning');
                return;
            }
            
            // 🔒 防抖检查 - 防止重复提交
            const currentTime = Date.now();
            if (streamState.qaSubmission.isSubmitting) {
                showNotification('正在处理中，请稍候...', 'info');
                return;
            }
            
            // 检查3秒冷却时间
            if (streamState.qaSubmission.submittedAt && 
                currentTime - streamState.qaSubmission.submittedAt < streamState.qaSubmission.cooldownMs) {
                const remainingSeconds = Math.ceil((streamState.qaSubmission.cooldownMs - (currentTime - streamState.qaSubmission.submittedAt)) / 1000);
                showNotification(`请等待${remainingSeconds}秒后再次提交`, 'warning');
                return;
            }
            
            // 检查重复提交（基于问题内容哈希）
            const questionHash = hashString(questionText);
            if (streamState.qaSubmission.submittedQuestions.has(questionHash)) {
                const lastSubmitted = streamState.qaSubmission.submittedQuestions.get(questionHash);
                if (currentTime - lastSubmitted < 30000) { // 30秒内不允许相同问题
                    showNotification('相同问题已提交，请等待回答或修改问题内容', 'warning');
                    return;
                }
            }
            
            // 检查是否有有效的会话ID
            if (!streamState.sessionId) {
                showNotification('请先启动直播流', 'warning');
                return;
            }
            
            try {
                // 🎯 乐观UI - 立即显示问题状态
                const optimisticQuestion = {
                    id: `optimistic_${Date.now()}`,
                    text: questionText,
                    priority: priority,
                    status: 'submitting',
                    submittedAt: currentTime,
                    viewer_name: '控制面板'
                };
                
                // 设置提交状态
                streamState.qaSubmission.isSubmitting = true;
                streamState.qaSubmission.submittedAt = currentTime;
                streamState.qaSubmission.submittedQuestions.set(questionHash, currentTime);
                streamState.qaSubmission.optimisticQuestions.push(optimisticQuestion);
                
                // 立即更新UI
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.textContent = '提交中...';
                }
                
                // 立即显示乐观UI
                updateOptimisticQuestionDisplay();
                showNotification(`正在处理您的问题: "${questionText.substring(0, 30)}..."`, 'info');
                
                // 清空输入框
                document.getElementById('questionText').value = '';
                
                // 使用动态的sessionId替代硬编码的'main' - 修复QA提交问题
                const response = await fetch(`/api/control/sessions/${streamState.sessionId}/questions`, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'X-Request-ID': optimisticQuestion.id // 用于请求追踪
                    },
                    body: JSON.stringify({
                        text: questionText,
                        priority: priority,
                        viewer_name: '控制面板'
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    // 更新乐观问题状态为成功
                    optimisticQuestion.status = 'submitted';
                    optimisticQuestion.serverResponse = result;
                    
                    updateOptimisticQuestionDisplay();
                    showNotification(`问题已提交成功，正在生成回答...`, 'success');
                    
                    // 5秒后移除乐观显示，让服务器状态接管
                    setTimeout(() => {
                        removeOptimisticQuestion(optimisticQuestion.id);
                        updateQuestionQueue();
                    }, 5000);
                    
                } else if (response.status === 404) {
                    // 会话已过期或不存在
                    optimisticQuestion.status = 'error';
                    optimisticQuestion.error = '会话已过期';
                    updateOptimisticQuestionDisplay();
                    
                    showNotification('直播会话已过期，请重新启动', 'error');
                    sessionStorage.removeItem('currentSessionId');
                    streamState.sessionId = null;
                    // 🔧 清除WebAudioPlayer中的过期session
                    if (webAudioPlayer && webAudioPlayer.clearSession) {
                        webAudioPlayer.clearSession();
                    }
                    
                    // 3秒后移除错误显示
                    setTimeout(() => removeOptimisticQuestion(optimisticQuestion.id), 3000);
                } else {
                    const errorData = await response.json();
                    optimisticQuestion.status = 'error';
                    optimisticQuestion.error = errorData.detail || '未知错误';
                    updateOptimisticQuestionDisplay();
                    
                    showNotification(`提交失败: ${errorData.detail || '未知错误'}`, 'error');
                    
                    // 3秒后移除错误显示
                    setTimeout(() => removeOptimisticQuestion(optimisticQuestion.id), 3000);
                }
                
            } catch (error) {
                // 网络错误处理
                const optimisticQuestion = streamState.qaSubmission.optimisticQuestions.find(q => q.text === questionText);
                if (optimisticQuestion) {
                    optimisticQuestion.status = 'error';
                    optimisticQuestion.error = `网络错误: ${error.message}`;
                    updateOptimisticQuestionDisplay();
                    
                    // 3秒后移除错误显示
                    setTimeout(() => removeOptimisticQuestion(optimisticQuestion.id), 3000);
                }
                
                showNotification(`网络错误: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态（3秒冷却）
                setTimeout(() => {
                    streamState.qaSubmission.isSubmitting = false;
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.textContent = '提交问题';
                    }
                }, streamState.qaSubmission.cooldownMs);
            }
        }

        // 简化的问题队列显示 - 基于design.md要求
        async function updateQuestionQueue() {
            try {
                const response = await fetch('/api/control/question-queue');
                const data = await response.json();

                const queueElement = document.getElementById('questionQueue');

                if (data.questions && data.questions.length > 0) {
                    queueElement.innerHTML = data.questions.map(q => `
                        <div class="question-item">
                            <div class="question-text">${q.text}</div>
                            <div class="question-status">${getSimpleStatus(q.status)}</div>
                            ${q.response_time_ms ? `<div class="response-time">${q.response_time_ms}ms</div>` : ''}
                            ${q.audio_status === 'failed' ? '<div class="audio-failed">🔇音频失败</div>' : ''}
                        </div>
                    `).join('');
                } else {
                    queueElement.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">暂无问题</div>';
                }
            } catch (error) {
                console.error('获取问题队列失败:', error);
                document.getElementById('questionQueue').innerHTML =
                    '<div style="text-align: center; color: #e74c3c; padding: 20px;">❌ 加载失败</div>';
            }
        }
        
        // 更新问题统计信息
        function updateQuestionStatistics(stats) {
            // 如果页面上有统计信息显示区域，更新它
            const statsElement = document.getElementById('questionStats');
            if (statsElement) {
                const successRate = (stats.success_rate * 100).toFixed(1);
                const audioSuccessRate = (stats.audio_success_rate * 100).toFixed(1);
                
                statsElement.innerHTML = `
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">${stats.total_processed}</div>
                            <div class="stat-label">总处理数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${successRate}%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${audioSuccessRate}%</div>
                            <div class="stat-label">音频成功率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${stats.average_response_time_ms}ms</div>
                            <div class="stat-label">平均响应时间</div>
                        </div>
                    </div>
                `;
            }
        }
        
        // 格式化时间显示
        function formatTime(isoString) {
            const date = new Date(isoString);
            const now = new Date();
            const diffMs = now - date;
            
            if (diffMs < 60000) { // 1分钟内
                return `${Math.floor(diffMs / 1000)}秒前`;
            } else if (diffMs < 3600000) { // 1小时内
                return `${Math.floor(diffMs / 60000)}分钟前`;
            } else {
                return date.toLocaleTimeString('zh-CN', { hour12: false });
            }
        }

        function getSimpleStatus(status) {
            const statusMap = {
                'pending': '等待中',
                'processing': '处理中', 
                'answered': '已回答',
                'answered_no_audio': '已回答(音频失败)',
                'ignored': '已忽略'
            };
            return statusMap[status] || '未知';
        }
        
        function getStatusClass(status) {
            const statusClassMap = {
                'pending': 'status-pending',
                'processing': 'status-processing',
                'answered': 'status-answered',
                'answered_no_audio': 'status-warning',
                'ignored': 'status-ignored'
            };
            return statusClassMap[status] || 'status-unknown';
        }

        function getPriorityText(priority) {
            const priorityMap = {
                'high': '高',
                'medium': '中',
                'low': '低'
            };
            return priorityMap[priority] || priority;
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                max-width: 300px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.2);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            
            // 设置颜色
            const colors = {
                'success': '#2ecc71',
                'error': '#e74c3c',
                'warning': '#f39c12',
                'info': '#3498db'
            };
            notification.style.backgroundColor = colors[type] || colors.info;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动消失
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case ' ':
                        e.preventDefault();
                        if (streamState.status === 'running') {
                            pauseStream();
                        } else if (streamState.status === 'paused') {
                            resumeStream();
                        }
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (streamState.status === 'stopped') {
                            startStream();
                        }
                        break;
                }
            }
        });

        // V2架构自动管理音频播放，移除手动跳过功能
        
        // 🚀 新增乐观UI相关函数
        
        // 简单哈希函数（用于问题去重）
        function hashString(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转为32位整数
            }
            return hash.toString();
        }
        
        // 更新乐观UI问题显示
        function updateOptimisticQuestionDisplay() {
            const queueElement = document.getElementById('questionQueue');
            if (!queueElement || streamState.qaSubmission.optimisticQuestions.length === 0) {
                return;
            }
            
            let optimisticHtml = '';
            streamState.qaSubmission.optimisticQuestions.forEach(question => {
                const statusText = {
                    'submitting': '⏳ 提交中...',
                    'submitted': '✅ 已提交，生成回答中...',
                    'error': '❌ 提交失败'
                }[question.status] || question.status;
                
                const statusClass = {
                    'submitting': 'optimistic-submitting',
                    'submitted': 'optimistic-submitted', 
                    'error': 'optimistic-error'
                }[question.status] || '';
                
                optimisticHtml += `
                    <div class="question-item optimistic-question ${statusClass}" data-id="${question.id}">
                        <div class="question-text">${question.text}</div>
                        <div class="question-status">${statusText}</div>
                        <div class="question-time">${formatTimeAgo(question.submittedAt)}</div>
                        ${question.error ? `<div class="error-detail">${question.error}</div>` : ''}
                    </div>
                `;
            });
            
            // 在现有问题队列前插入乐观问题
            const existingContent = queueElement.innerHTML;
            if (existingContent.includes('暂无问题')) {
                queueElement.innerHTML = optimisticHtml;
            } else {
                // 检查是否已有乐观问题，避免重复
                const existingOptimistic = queueElement.querySelectorAll('.optimistic-question');
                existingOptimistic.forEach(el => el.remove());
                queueElement.insertAdjacentHTML('afterbegin', optimisticHtml);
            }
        }
        
        // 移除乐观问题
        function removeOptimisticQuestion(questionId) {
            streamState.qaSubmission.optimisticQuestions = 
                streamState.qaSubmission.optimisticQuestions.filter(q => q.id !== questionId);
            
            const questionElement = document.querySelector(`[data-id="${questionId}"]`);
            if (questionElement) {
                questionElement.style.transition = 'opacity 0.5s ease-out';
                questionElement.style.opacity = '0';
                setTimeout(() => questionElement.remove(), 500);
            }
        }
        
        // 时间格式化函数（更精确）
        function formatTimeAgo(timestamp) {
            const seconds = Math.floor((Date.now() - timestamp) / 1000);
            if (seconds < 10) return '刚刚';
            if (seconds < 60) return `${seconds}秒前`;
            const minutes = Math.floor(seconds / 60);
            return `${minutes}分钟前`;
        }
        
        // 定期更新问题队列 - 简化为2秒间隔
        setInterval(updateQuestionQueue, 2000);
        
        // 调试函数：手动触发句子显示测试
        window.testSentenceDisplay = function() {
            console.log('🧪 手动测试句子显示功能');
            const testContent = {
                segment_title: '测试段落标题',
                content_preview: '这是测试内容预览',
                segment_sentences: [
                    '这是第一句测试句子。',
                    '这是第二句测试句子！',  
                    '这是第三句测试句子？',
                    '这是最后一句测试句子。'
                ],
                current_sentence_index: 1,
                current_sentence_text: '这是第二句测试句子！',
                total_sentences_in_segment: 4
            };
            console.log('测试数据:', testContent);
            updateSentenceDisplay(testContent);
        };
        
        // 初始化音频流功能
        async function initializeAudioStreaming() {
            try {
                console.log('🚀 Starting audio streaming initialization...');
                
                // Step 1: Check WebAudioPlayer availability
                console.debug("[DIAGNOSTIC] Checking for WebAudioPlayer. Current type:", typeof WebAudioPlayer);
                if (typeof WebAudioPlayer === 'undefined') {
                    console.error('❌ WebAudioPlayer class not available');
                    updateAudioStreamStatus('error', 'WebAudioPlayer不可用 - 检查静态文件加载');
                    return;
                }
                console.log('✅ WebAudioPlayer class available');
                
                // Step 2: Create WebAudioPlayer instance
                console.log('🔍 Creating WebAudioPlayer instance...');
                webAudioPlayer = new WebAudioPlayer();
                console.log('✅ WebAudioPlayer instance created');
                
                // Step 3: Set up event handlers before initialization
                webAudioPlayer.onError = (error, category) => {
                    console.error(`❌ WebAudioPlayer error [${category}]: ${error}`);
                    
                    if (category === 'user_interaction_required') {
                        updateAudioStreamStatus('needs_interaction', '需要用户交互启用音频');
                        const enableAudioBtn = document.getElementById('enableAudioPlayBtn');
                        if (enableAudioBtn) {
                            enableAudioBtn.style.display = 'inline-block';
                        }
                    } else {
                        updateAudioStreamStatus('error', `音频初始化失败: ${error}`);
                    }
                };
                
                webAudioPlayer.onConnected = () => {
                    console.log('✅ WebSocket connected successfully');
                    updateAudioStreamStatus('connected', '音频流已连接');
                };
                
                webAudioPlayer.onDisconnected = () => {
                    console.log('⚠️ WebSocket disconnected');
                    
                    // 🔧 架构师建议：断开连接时的安全网处理
                    if (streamState.sessionId) {
                        console.log('🧹 WebSocket意外断开，清理会话状态...');
                        sessionStorage.removeItem('currentSessionId');
                        streamState.sessionId = null;
                        // 🔧 清除WebAudioPlayer中的过期session
                        if (webAudioPlayer && webAudioPlayer.clearSession) {
                            webAudioPlayer.clearSession();
                        }
                        resetUIToInitialState();
                        showNotification('连接意外中断，请重新开始直播', 'warning');
                    } else {
                        updateAudioStreamStatus('disconnected', '音频流连接中断');
                    }
                };
                
                // WebSocket架构重构：添加服务端状态更新消息处理器
                webAudioPlayer.onServerStateUpdate = (stateData) => {
                    console.log('📨 Received server state update via WebSocket');
                    handleServerStateUpdate(stateData);
                };
                
                // Step 4: Wait for initialization to complete
                console.log('🔍 Waiting for WebAudioPlayer initialization...');
                updateAudioStreamStatus('initializing', '正在初始化音频系统...');
                
                // WebAudioPlayerV2 API修复: 使用initialize()替代waitForInitialization()
                const initSuccess = await webAudioPlayer.initialize();
                
                if (!initSuccess) {
                    console.error('❌ WebAudioPlayer initialization failed');
                    // WebAudioPlayerV2简化的错误处理
                    updateAudioStreamStatus('error', '音频初始化失败，请刷新页面重试');
                    return;
                }
                
                console.log('✅ WebAudioPlayer initialization successful');
                
                // Step 5: Check final connection status
                // WebAudioPlayerV2 API修复: 通过connection子模块访问isConnected
                if (webAudioPlayer.connection && webAudioPlayer.connection.isConnected) {
                    updateAudioStreamStatus('ready', '音频流就绪');
                    console.log('✅ Audio streaming ready for use');
                } else {
                    updateAudioStreamStatus('connecting', '正在连接音频服务...');
                    console.log('⏳ Waiting for WebSocket connection...');
                    
                    // 等待一段时间后再次检查连接状态
                    setTimeout(() => {
                        if (webAudioPlayer && webAudioPlayer.connection && webAudioPlayer.connection.isConnected) {
                            updateAudioStreamStatus('ready', '音频流就绪');
                            console.log('✅ WebSocket connection established after delay');
                        } else {
                            updateAudioStreamStatus('connection_timeout', '音频服务连接超时 - 可能需要手动重试');
                            console.warn('⚠️ WebSocket connection timeout');
                        }
                    }, 5000); // 5秒超时
                }
                
                console.log('✅ WebAudioPlayer initialized successfully');
                
                // 设置事件监听器
                webAudioPlayer.onStatusUpdate = (status) => {
                    streamState.audioStreaming.status = status;
                    updateAudioStreamStatus(status);
                    
                    // 处理Q&A音频状态
                    if (status.qaAudioActive !== undefined) {
                        streamState.audioStreaming.qaAudioActive = status.qaAudioActive;
                        if (status.qaAudioActive) {
                            updateAudioStreamStatus('qa_active', `Q&A模式: ${status.qaTextPreview || '问答中'}`);
                        }
                    }
                    
                    if (status.qaAudioCompleted) {
                        updateAudioStreamStatus('qa_completed', 'Q&A完成，已恢复直播音频');
                    }
                    
                    if (status.qaAudioError) {
                        updateAudioStreamStatus('qa_error', `Q&A错误: ${status.qaError}`);
                    }
                    
                    if (status.liveAudioPaused !== undefined) {
                        streamState.audioStreaming.liveAudioPaused = status.liveAudioPaused;
                        if (status.liveAudioPaused) {
                            updateAudioStreamStatus('live_paused', '直播音频已暂停');
                        }
                    }
                    
                    if (status.liveAudioResumed) {
                        updateAudioStreamStatus('live_resumed', '直播音频已恢复');
                    }
                    
                    // 显示/隐藏音频播放启动按钮
                    const enableAudioBtn = document.getElementById('enableAudioPlayBtn');
                    if (status === 'autoplay_blocked' || status === 'needs_interaction') {
                        enableAudioBtn.style.display = 'inline-block';
                        if (status === 'autoplay_blocked') {
                            updateAudioStreamStatus('autoplay_blocked', '需要用户交互启动播放');
                        } else {
                            updateAudioStreamStatus('needs_interaction', '需要用户交互启用音频');
                        }
                    } else if (status === 'playing') {
                        enableAudioBtn.style.display = 'none';
                    }
                };
                
                webAudioPlayer.onError = (error, category) => {
                    console.error('Audio player error:', error, 'Category:', category);
                    
                    // Provide specific handling based on error category
                    if (category === 'user_interaction_required') {
                        updateAudioStreamStatus('needs_interaction', error);
                        // Show enable button
                        const enableAudioBtn = document.getElementById('enableAudioPlayBtn');
                        enableAudioBtn.style.display = 'inline-block';
                    } else if (category === 'audio_not_supported') {
                        updateAudioStreamStatus('not_supported', `浏览器不兼容: ${error}`);
                        showNotification('浏览器不支持音频功能，请使用Chrome、Firefox或Safari', 'error');
                    } else if (category === 'media_source') {
                        updateAudioStreamStatus('media_error', `媒体错误: ${error}`);
                    } else if (category === 'websocket') {
                        updateAudioStreamStatus('connection_error', `连接错误: ${error}`);
                    } else {
                        updateAudioStreamStatus('error', `音频系统错误: ${error}`);
                    }
                };
                
                webAudioPlayer.onConnected = () => {
                    streamState.audioStreaming.isConnected = true;
                    updateAudioStreamStatus('connected', '音频流已连接');
                    updateAudioStreamButtons();
                };
                
                webAudioPlayer.onDisconnected = () => {
                    streamState.audioStreaming.isConnected = false;
                    streamState.audioStreaming.isPlaying = false;
                    
                    // 🔧 架构师建议：WebSocket断开时执行完整的状态清理（安全网）
                    if (streamState.sessionId) {
                        console.log('🧹 WebSocket断开，清理会话状态...');
                        sessionStorage.removeItem('currentSessionId');
                        streamState.sessionId = null;
                        // 🔧 清除WebAudioPlayer中的过期session（注意：这里不需要再调用，因为resetUIToInitialState已经会调用）
                        
                        // 重置UI到初始状态，确保用户可以重新开始
                        resetUIToInitialState();
                        
                        // 显示友好的提示
                        showNotification('连接中断，请点击"开始直播"重新开始', 'warning');
                    } else {
                        // 正常的断开连接
                        updateAudioStreamStatus('disconnected', '音频流已断开');
                        updateAudioStreamButtons();
                    }
                };
                
                console.log('Audio streaming initialized');
                
            } catch (error) {
                console.error('❌ Failed to initialize audio streaming:', error);
                updateAudioStreamStatus('error', `初始化失败: ${error.message}`);
            }
        }
        
        // 启动音频流
        async function startAudioStreaming() {
            if (!webAudioPlayer) {
                showNotification('音频播放器未初始化', 'warning');
                return;
            }
            
            // 获取按钮状态，确保 finally 块可以访问
            const startBtn = document.getElementById('startAudioStreamBtn');
            const originalText = startBtn.textContent;
            
            try {
                // 显示加载状态
                startBtn.textContent = '正在启动...';
                startBtn.disabled = true;
                
                showNotification('正在启动音频流...', 'info');
                
                // 构建请求体，仅包含后端 StartStreamRequest 支持的参数
                const requestBody = {};

                // 添加内容参数：优先使用当前脚本的 form_id
                if (streamState.sourceFormId) {
                    requestBody.mode = 'script_based';  // 指定使用脚本模式
                    requestBody.form_id = streamState.sourceFormId;
                    console.log('🎬 Using form_id for streaming:', streamState.sourceFormId);
                } else {
                    // 如果没有 form_id，则抛出错误要求用户先加载脚本
                    throw new Error('请先加载一个脚本再启动播放');
                }

                // 调用后端V2 API启动音频流
                const response = await fetch('/api/control/start-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '启动音频流失败');
                }
                
                const result = await response.json();
                console.log('🚀 Audio streaming started:', result);
                
                // 使用 SessionManager 管理会话状态
                if (result.session_id) {
                    if (window.sessionManager) {
                        // 使用 SessionManager 启动会话
                        window.sessionManager.startSession(result.session_id);
                        console.log('📝 Session started via SessionManager:', result.session_id);
                    } else {
                        // 降级处理：直接保存
                        streamState.sessionId = result.session_id;
                        sessionStorage.setItem('currentSessionId', result.session_id);
                        console.log('📝 Session ID saved (fallback):', result.session_id);
                    }
                }
                
                // V2 Protocol Fix: Connect WebSocket AFTER start-stream succeeds
                // This ensures playlist is initialized before WebSocket connection
                if (result.session_id && webAudioPlayer) {
                    console.log('🔌 Connecting WebSocket with session:', result.session_id);
                    try {
                        // WebAudioPlayerV2 API修复: 使用connectAndPlay替代connectWithSession
                        await webAudioPlayer.connectAndPlay(result.session_id, {
                            client_type: 'control_panel'
                        });
                        console.log('✅ WebSocket connected successfully');
                        streamState.audioStreaming.isConnected = true;  // 现在设置连接状态
                    } catch (wsError) {
                        console.error('❌ Failed to connect WebSocket:', wsError);
                        
                        // 使用 SessionManager 清理失败的会话
                        console.log('🧹 WebSocket连接失败，清理session状态...');
                        if (window.sessionManager) {
                            window.sessionManager.endSession('error');
                        } else {
                            // 降级处理
                            sessionStorage.removeItem('currentSessionId');
                            streamState.sessionId = null;
                        }
                        streamState.audioStreaming.isConnected = false;
                        
                        // 检查错误类型，提供更友好的提示
                        let userMessage;
                        if (wsError.message && wsError.message.includes('403')) {
                            userMessage = '连接失败或会话已过期，请点击"开始直播"重新开始';
                        } else {
                            userMessage = 'WebSocket连接失败，请检查网络连接后重试';
                        }
                        
                        showNotification(userMessage, 'error');
                        
                        // 重置UI到初始状态，让用户可以重新开始
                        resetUIToInitialState();
                        
                        // 如果WebSocket连接失败，需要停止流
                        throw new Error('WebSocket连接失败');
                    }
                }
                
                streamState.audioStreaming.isPlaying = true;
                updateAudioStreamButtons();
                showNotification(`音频流启动成功！${result.message || ''}`, 'success');
                
            } catch (error) {
                console.error('Failed to start audio streaming:', error);
                showNotification(`启动音频流失败: ${error.message}`, 'error');
                
                // 恢复按钮状态
                streamState.audioStreaming.isPlaying = false;
                updateAudioStreamButtons();
                
            } finally {
                // 恢复按钮状态
                startBtn.textContent = originalText;
                startBtn.disabled = false;
            }
        }
        
        // V2架构音频流通过WebSocket管理，移除独立的停止功能
        
        // 启动音频播放（处理自动播放限制和AudioContext初始化）
        async function enableAudioPlayback() {
            if (!webAudioPlayer) {
                showNotification('音频播放器未初始化', 'warning');
                return;
            }
            
            try {
                console.log('🔊 Initializing audio system with user interaction...');
                
                // Use the new method that handles both AudioContext and playback
                // WebAudioPlayerV2 API修复: 直接调用initialize，不需要单独的用户交互方法
                const success = await webAudioPlayer.initialize();
                
                if (success) {
                    showNotification('音频系统已启用', 'success');
                    
                    // 隐藏启动按钮
                    const enableAudioBtn = document.getElementById('enableAudioPlayBtn');
                    enableAudioBtn.style.display = 'none';
                    
                    // 更新状态显示
                    if (webAudioPlayer.isConnected) {
                        updateAudioStreamStatus('connected', '音频流已连接并可播放');
                    } else {
                        updateAudioStreamStatus('ready', '音频系统已就绪');
                    }
                } else {
                    showNotification('音频系统启用失败', 'error');
                }
            } catch (error) {
                console.error('Failed to enable audio system:', error);
                let errorMessage = error.message;
                if (errorMessage.includes('AudioContext')) {
                    errorMessage = '音频上下文创建失败，请检查浏览器设置';
                } else if (errorMessage.includes('user interaction')) {
                    errorMessage = '需要用户交互，请重试';
                }
                showNotification(`音频系统启用失败: ${errorMessage}`, 'error');
            }
        }
        
        // 测试音频流
        async function testAudioStreaming() {
            if (!webAudioPlayer) {
                showNotification('音频播放器未初始化', 'warning');
                return;
            }
            
            const status = webAudioPlayer.getStatus();
            console.log('Audio player status:', status);
            
            const testInfo = [
                `连接状态: ${status.isConnected ? '已连接' : '未连接'}`,
                `播放状态: ${status.isPlaying ? '播放中' : '未播放'}`,
                `当前格式: ${status.currentFormat}`,
                `队列长度: ${status.queueLength}`,
                `缓冲范围: ${status.bufferedRanges}`,
                `MediaSource状态: ${status.mediaSourceState}`
            ].join('\n');
            
            showNotification('测试信息已输出到控制台', 'info');
            alert(`音频流状态:\n${testInfo}`);
        }
        
        // 更新音频流状态显示
        function updateAudioStreamStatus(status, message = null) {
            const infoElement = document.getElementById('audioStreamInfo');
            
            if (infoElement && message) {
                infoElement.textContent = message;
            }
            
        }

        
        // 更新音频流按钮状态
        function updateAudioStreamButtons() {
            const startBtn = document.getElementById('startAudioStreamBtn');
            
            if (startBtn) {
                const canStart = streamState.audioStreaming.isConnected && !streamState.audioStreaming.isPlaying;
                startBtn.disabled = !canStart;
            }
        }
        
        
        // 在控制台中提供调试提示
        console.log('💡 调试提示：在控制台中运行 testSentenceDisplay() 来测试句子显示功能');
        console.log('🎵 音频流调试：运行 testAudioStreaming() 来测试音频流功能');