<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播运营配置系统</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 16px;
        }

        .progress-bar {
            background: var(--border-color);
            border-radius: 8px;
            height: 8px;
            margin-top: 16px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            height: 100%;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* Save Status Indicator Styles */
        .save-status {
            text-align: center;
            margin-top: 12px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .save-status.saving {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .save-status.saved {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .save-status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Field error highlighting styles */
        .field-error {
            border: 2px solid #e74c3c !important;
            background-color: #fdf2f2 !important;
            box-shadow: 0 0 5px rgba(231, 76, 60, 0.3) !important;
        }

        .field-error:focus {
            border-color: #c0392b !important;
            box-shadow: 0 0 8px rgba(231, 76, 60, 0.5) !important;
        }

        .field-error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .field-error-message::before {
            content: "⚠ ";
            margin-right: 4px;
        }

        /* Field success highlighting styles */
        .field-success {
            border: 2px solid #27ae60 !important;
            background-color: #f8fff8 !important;
            box-shadow: 0 0 5px rgba(39, 174, 96, 0.3) !important;
        }

        .field-success:focus {
            border-color: #229954 !important;
            box-shadow: 0 0 8px rgba(39, 174, 96, 0.5) !important;
        }

        .field-success-message {
            color: #27ae60;
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
        }

        /* Clear error styling when field is corrected */
        input:not(.field-error), textarea:not(.field-error), select:not(.field-error) {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Validation message base styles */
        .field-validation-message {
            font-size: 12px;
            margin-top: 4px;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }

        .save-status-icon {
            margin-right: 6px;
        }

        .save-timestamp {
            font-size: 10px;
            opacity: 0.7;
            margin-left: 8px;
        }

        /* Script Readiness Indicator Styles */
        .script-readiness {
            text-align: center;
            margin-top: 8px;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 11px;
            transition: all 0.3s ease;
        }

        .script-readiness.ready {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .script-readiness.not-ready {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .script-readiness.template-data {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .readiness-icon {
            margin-right: 4px;
        }

        .readiness-details {
            font-size: 9px;
            opacity: 0.8;
            margin-left: 6px;
        }

        /* Manual Save Notice Styles */
        .manual-save-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            margin: 12px 0;
            text-align: center;
        }

        .manual-save-notice span {
            margin-right: 6px;
        }

        .section-nav {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
            overflow-x: auto;
            padding: 4px;
        }

        .section-nav button {
            flex: 1;
            min-width: 160px;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            background: var(--card-bg);
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .section-nav button:hover {
            border-color: var(--primary-color);
            background: #f1f5f9;
        }

        .section-nav button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .section-nav button.completed {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .form-section {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .section-description {
            color: var(--text-secondary);
            margin-bottom: 32px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .required {
            color: var(--error-color);
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .input-hint {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .char-count {
            font-size: 0.85em;
            margin-left: 10px;
            font-weight: 500;
        }

        .char-count-insufficient {
            color: #ff4444;
        }

        .char-count-exceeded {
            color: #ff4444;
        }

        .char-count-valid {
            color: #28a745;
        }

        .selling-points-container {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .selling-point {
            background: #f8fafc;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }

        .selling-point-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 16px;
        }

        .selling-point-title {
            font-weight: 600;
            font-size: 16px;
        }

        .priority-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-high {
            background: #fef2f2;
            color: var(--error-color);
        }

        .priority-medium {
            background: #fffbeb;
            color: var(--warning-color);
        }

        .priority-low {
            background: #f0fdf4;
            color: var(--success-color);
        }

        .facts-list {
            margin-top: 12px;
        }

        .fact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background: #475569;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid var(--border-color);
        }

        .validation-summary {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .validation-summary.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }

        .validation-summary.warning {
            background: #fffbeb;
            border-color: #fed7aa;
        }

        .validation-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .validation-list {
            list-style: none;
        }

        .validation-list li {
            margin-bottom: 4px;
            color: var(--error-color);
        }

        .validation-list.success li {
            color: var(--success-color);
        }

        .persona-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .persona-card {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .persona-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .persona-card.selected {
            border-color: var(--primary-color);
            background: #f1f5f9;
        }

        .persona-name {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .persona-description {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .hidden {
            display: none;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }

        .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--error-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>AI直播运营配置系统</h1>
            <p>配置您的AI直播解说系统，包含产品信息、卖点结构、人设选择等6个核心模块</p>
            <div class="manual-save-notice">
                <span>💾</span> 请注意：系统已关闭自动保存，请使用 "保存当前节" 或 "保存全部" 按钮手动保存您的工作
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">完成度: 0%</div>
            
            <!-- Save Status Indicator -->
            <div class="save-status" id="saveStatus" style="display: none;">
                <span class="save-status-icon" id="saveStatusIcon">💾</span>
                <span class="save-status-text" id="saveStatusText">已保存</span>
                <span class="save-timestamp" id="saveTimestamp"></span>
            </div>
            
            <!-- Script Generation Readiness Indicator -->
            <div class="script-readiness" id="scriptReadiness" style="display: none;">
                <span class="readiness-icon" id="readinessIcon">📝</span>
                <span class="readiness-text" id="readinessText">脚本生成就绪</span>
                <span class="readiness-details" id="readinessDetails"></span>
            </div>
        </div>

        <!-- Section Navigation -->
        <div class="section-nav">
            <button class="active" onclick="showSection(1)">1. 基础信息</button>
            <button onclick="showSection(2)">2. 商品信息</button>
            <button onclick="showSection(3)">3. 卖点结构</button>
            <button onclick="showSection(4)">4. 人设配置</button>
            <button onclick="showSection(5)">5. 高级设置</button>
            <button onclick="showSection(6)">6. 审核提交</button>
        </div>

        <!-- Form Sections -->
        <div id="formContainer">
            <!-- Section 1: Basic Information -->
            <div id="section1" class="form-section">
                <h2 class="section-title">基础信息配置</h2>
                <p class="section-description">设置直播的基本信息，包括标题、类型、时长等核心参数</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="streamTitle">直播标题 <span class="required">*</span></label>
                        <input type="text" id="streamTitle" name="stream_title" placeholder="请输入吸引人的直播标题">
                        <div class="input-hint">标题将显示给观众，建议包含产品关键词</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="streamType">直播类型 <span class="required">*</span></label>
                        <select id="streamType">
                            <option value="">请选择直播类型</option>
                            <option value="product_launch">新品发布</option>
                            <option value="daily_stream">日常直播</option>
                            <option value="flash_sale">限时特卖</option>
                            <option value="special_event">特殊活动</option>
                            <option value="q_and_a">问答互动</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="plannedDuration">计划时长（分钟） <span class="required">*</span></label>
                        <input type="number" id="plannedDuration" min="30" max="180" value="90">
                        <div class="input-hint">建议60-120分钟，系统会自动调节内容节奏</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="targetTimezone">目标时区</label>
                        <select id="targetTimezone">
                            <option value="Asia/Tokyo">Asia/Tokyo (东京时间)</option>
                            <option value="Asia/Shanghai">Asia/Shanghai (北京时间)</option>
                            <option value="Asia/Seoul">Asia/Seoul (首尔时间)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableAutoInteraction" checked>
                        启用自动互动（推荐）
                    </label>
                    <div class="input-hint">AI会自动回应观众问题和进行主动互动</div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-outline" onclick="saveCurrentSection()">💾 保存当前节</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一步：商品信息</button>
                </div>
            </div>

            <!-- Section 2: Product Information -->
            <div id="section2" class="form-section hidden">
                <h2 class="section-title">商品信息配置</h2>
                <p class="section-description">详细配置主推商品的信息，包括SKU、价格、规格等关键数据</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="primarySku">主推商品SKU <span class="required">*</span></label>
                        <input type="text" id="primarySku" name="primary_sku" placeholder="如：ABC-123-XL">
                        <div class="input-hint">商品的唯一标识符，用于库存和价格查询</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="productName">商品名称 <span class="required">*</span></label>
                        <input type="text" id="productName" name="product_name" placeholder="请输入完整的商品名称">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="brand">品牌 <span class="required">*</span></label>
                        <input type="text" id="brand" name="brand" placeholder="商品品牌">
                    </div>
                    
                    <div class="form-group">
                        <label for="category">商品类别 <span class="required">*</span></label>
                        <input type="text" id="category" name="category" placeholder="如：电子产品、服装、家具">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="currentPrice">当前售价 <span class="required">*</span></label>
                        <input type="number" id="currentPrice" name="current_price" step="0.01" min="0" placeholder="0.00">
                    </div>
                    
                    <div class="form-group">
                        <label for="originalPrice">原价（如有优惠）</label>
                        <input type="number" id="originalPrice" name="original_price" step="0.01" min="0" placeholder="0.00">
                        <div class="input-hint">留空表示无优惠活动</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="keySpecifications">关键规格 <span class="required">*</span></label>
                    <textarea id="keySpecifications" name="key_specifications" placeholder="每行一个规格，如：&#10;颜色：红色&#10;尺寸：L码&#10;材质：纯棉"></textarea>
                    <div class="input-hint">每行输入一个规格，系统会自动解析</div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一步</button>
                    <button class="btn btn-outline" onclick="saveCurrentSection()">💾 保存当前节</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一步：卖点结构</button>
                </div>
            </div>

            <!-- Section 3: Selling Points Structure -->
            <div id="section3" class="form-section hidden">
                <h2 class="section-title">卖点结构配置</h2>
                <p class="section-description">构建商品的核心卖点体系，包括主价值主张、支撑事实和应对策略</p>
                
                <div class="form-group">
                    <label for="valueProposition">主价值主张 <span class="required">*</span></label>
                    <textarea id="valueProposition" name="primary_value_proposition" placeholder="用1-2句话概括商品的核心价值，如：高品质、低价格、独特功能等" oninput="updateCharacterCount('valueProposition', 'valuePropositionCount', 20, 300)"></textarea>
                    <div class="input-hint">
                        这是整个直播的核心论点，会贯穿始终
                        <span id="valuePropositionCount" class="char-count">0/20 (最少20个字符)</span>
                    </div>
                </div>
                
                <div class="selling-points-container">
                    <h3>核心卖点 <span class="required">*</span> <button class="btn btn-outline" onclick="addSellingPoint()">添加卖点</button></h3>
                    <div id="sellingPointsList">
                        <!-- Selling points will be added here dynamically -->
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="competitiveAdvantages">竞争优势</label>
                        <textarea id="competitiveAdvantages" placeholder="每行一个优势，如：&#10;比竞品A便宜20%&#10;独有专利技术&#10;更长的质保期"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="callToActions">行动号召语</label>
                        <textarea id="callToActions" name="call_to_actions" placeholder="每行一个CTA，如：&#10;立即下单享优惠&#10;限时特价，数量有限&#10;点击下方链接购买"></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一步</button>
                    <button class="btn btn-outline" onclick="saveCurrentSection()">💾 保存当前节</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一步：人设配置</button>
                </div>
            </div>

            <!-- Section 4: Persona Configuration -->
            <div id="section4" class="form-section hidden">
                <h2 class="section-title">人设配置</h2>
                <p class="section-description">选择AI主播的人设风格、语音特征和互动方式</p>
                
                <div class="form-group">
                    <label>选择人设模板 <span class="required">*</span></label>
                    <div class="persona-selector" id="personaSelector">
                        <!-- Personas will be loaded here -->
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="speakingRate">语速调节</label>
                        <input type="range" id="speakingRate" min="0.5" max="2.0" step="0.1" value="1.0">
                        <div class="input-hint">当前值: <span id="speakingRateValue">1.0</span> (1.0为正常语速)</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="energyLevel">能量等级</label>
                        <select id="energyLevel">
                            <option value="low">低能量（沉稳）</option>
                            <option value="medium" selected>中等能量（自然）</option>
                            <option value="high">高能量（激情）</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="customGreetings">自定义开场白（可选）</label>
                    <textarea id="customGreetings" placeholder="每行一个开场白，系统会随机选择使用&#10;如：大家好，欢迎来到我的直播间！&#10;今天要给大家介绍一款超棒的商品"></textarea>
                    <div class="input-hint">留空将使用默认人设的开场白</div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一步</button>
                    <button class="btn btn-outline" onclick="saveCurrentSection()">💾 保存当前节</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一步：高级设置</button>
                </div>
            </div>

            <!-- Section 5: Advanced Settings -->
            <div id="section5" class="form-section hidden">
                <h2 class="section-title">高级设置</h2>
                <p class="section-description">微调系统行为参数，优化直播效果（可选配置）</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="wordsPerMinute">语速覆盖（每分钟字数）</label>
                        <input type="number" id="wordsPerMinute" min="100" max="300" placeholder="留空使用自动计算">
                        <div class="input-hint">系统会根据观众人数自动调整，一般无需手动设置</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="questionTimeout">问答超时时间（秒）</label>
                        <input type="number" id="questionTimeout" min="60" max="900" value="300">
                        <div class="input-hint">超时未回答的问题将被跳过</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableFactChecking" checked>
                        启用实时事实核查
                    </label>
                    <div class="input-hint">AI会验证商品信息的准确性</div>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enablePriceUpdates" checked>
                        启用实时价格更新
                    </label>
                    <div class="input-hint">自动获取最新价格信息</div>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableDiversityEngine" checked>
                        启用内容多样性检查
                    </label>
                    <div class="input-hint">避免重复提及相同内容</div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一步</button>
                    <button class="btn btn-outline" onclick="saveCurrentSection()">💾 保存当前节</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一步：审核提交</button>
                </div>
            </div>

            <!-- Section 6: Review and Validation -->
            <div id="section6" class="form-section hidden">
                <h2 class="section-title">审核与提交</h2>
                <p class="section-description">检查配置完整性，验证合规性，准备提交配置</p>
                
                <div id="validationSummary" class="validation-summary">
                    <div class="validation-title">配置验证中...</div>
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="contentReviewCompleted">
                            内容审核已完成
                        </label>
                        <div class="input-hint">确认所有卖点和描述准确无误</div>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="complianceReviewCompleted">
                            合规性审核已完成
                        </label>
                        <div class="input-hint">确认符合平台规范和法律要求</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="approvalNotes">审批备注（可选）</label>
                    <textarea id="approvalNotes" placeholder="记录审批过程中的重要信息或修改说明"></textarea>
                </div>
                
                <!-- Form ID Info (Initially Hidden) -->
                <div id="formIdInfo" class="form-group" style="display: none;">
                    <label>表单ID</label>
                    <div style="display: flex; align-items: center; gap: 10px; margin-top: 8px;">
                        <input type="text" id="formIdDisplay" readonly style="flex: 1; background: #f8f9fa; cursor: text;">
                        <button type="button" class="btn btn-secondary" onclick="copyFormId()" style="padding: 8px 16px;">复制</button>
                        <button type="button" class="btn btn-primary" onclick="generateScript()" style="padding: 8px 16px;">生成脚本</button>
                    </div>
                    <div class="input-hint">保存此ID以生成脚本和管理配置</div>
                </div>
                
                <div class="form-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一步</button>
                    <button class="btn btn-outline" onclick="saveAllSections()">💾 保存全部</button>
                    <button class="btn btn-success" id="submitBtn" onclick="submitForm()" disabled>提交配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        // Global state
        let currentSection = 1;
        let formData = {};
        let sellingPointsCount = 0;
        let availablePersonas = [];
        let formId = null;
        let sessionId = null;
        
        // Memory leak prevention: track event listeners and intervals for cleanup
        const managedEventListeners = [];
        const managedIntervals = [];
        const managedTimeouts = [];

        // Helper functions for memory management
        function addManagedEventListener(element, event, handler, options = {}) {
            element.addEventListener(event, handler, options);
            managedEventListeners.push({ element, event, handler, options });
        }

        function addManagedInterval(callback, interval) {
            const id = setInterval(callback, interval);
            managedIntervals.push(id);
            return id;
        }

        function addManagedTimeout(callback, timeout) {
            const id = setTimeout(callback, timeout);
            managedTimeouts.push(id);
            return id;
        }

        function cleanupResources() {
            // Remove all managed event listeners
            managedEventListeners.forEach(({ element, event, handler, options }) => {
                try {
                    element.removeEventListener(event, handler, options);
                } catch (e) {
                    console.warn('Failed to remove event listener:', e);
                }
            });
            managedEventListeners.length = 0;

            // Clear all managed intervals
            managedIntervals.forEach(id => {
                try {
                    clearInterval(id);
                } catch (e) {
                    console.warn('Failed to clear interval:', e);
                }
            });
            managedIntervals.length = 0;

            // Clear all managed timeouts
            managedTimeouts.forEach(id => {
                try {
                    clearTimeout(id);
                } catch (e) {
                    console.warn('Failed to clear timeout:', e);
                }
            });
            managedTimeouts.length = 0;

            console.log('Cleaned up all managed resources');
        }

        // Add cleanup on page unload to prevent memory leaks
        addManagedEventListener(window, 'beforeunload', cleanupResources);
        addManagedEventListener(window, 'unload', cleanupResources);
        
        // Also cleanup on visibility change (when tab becomes hidden)
        addManagedEventListener(document, 'visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                // User switched away, cleanup non-essential resources
                console.log('Page hidden, cleaning up non-essential resources');
            }
        });

        // Unified initialization - handles both new form creation and existing form editing
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Check if form_id is provided in URL params (for editing)
                const urlParams = new URLSearchParams(window.location.search);
                const formIdFromUrl = urlParams.get('form_id');
                
                if (formIdFromUrl) {
                    // Edit mode: Load existing form
                    console.log(`Edit mode: Using existing form ID ${formIdFromUrl}`);
                    formId = formIdFromUrl;
                    
                    // Validate form exists before loading
                    const formExists = await validateFormExists(formIdFromUrl);
                    if (formExists) {
                        await loadExistingFormData(formIdFromUrl);
                        showFormIdInfo(formIdFromUrl);
                        showNotification('加载现有表单成功', 'success');
                        console.log(`Successfully loaded form ${formIdFromUrl} for editing`);
                    } else {
                        throw new Error(`表单 ${formIdFromUrl} 不存在或已被删除`);
                    }
                } else {
                    // Create mode: Initialize new form
                    console.log('Create mode: Initializing new form');
                    await initializeForm();
                    console.log(`Created new form with ID ${formId}`);
                }
                
                // Common initialization for both modes
                loadPersonas();
                setupEventListeners();
                
            } catch (error) {
                console.error('初始化失败:', error);
                showNotification('初始化失败: ' + error.message, 'error');
            }
        });

        function setupEventListeners() {
            // Speaking rate slider
            document.getElementById('speakingRate').addEventListener('input', function(e) {
                document.getElementById('speakingRateValue').textContent = e.target.value;
            });

            // No auto-save - only manual save through buttons
            // Users must click "💾 保存当前节" or "💾 保存全部" to save their work
        }


        async function initializeForm() {
            // Create new form - only called when no form_id in URL
            const response = await fetch('/api/operational/forms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    created_by: 'demo_user'
                })
            });

            if (!response.ok) {
                throw new Error(`创建表单失败: ${response.status}`);
            }

            const result = await response.json();
            formId = result.form_id;
            
            // Create session for new form
            await createSession();
            
            showNotification('新表单创建成功', 'success');
        }

        async function createSession() {
            try {
                const response = await fetch('/api/operational/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 'demo_user',
                        form_id: formId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    sessionId = result.session_id;
                } else {
                    throw new Error('Failed to create session');
                }
            } catch (error) {
                console.error('Session creation error:', error);
            }
        }

        async function loadPersonas() {
            try {
                const response = await fetch('/api/operational/personas');
                if (response.ok) {
                    const result = await response.json();
                    availablePersonas = result.personas;
                    renderPersonaSelector();
                }
            } catch (error) {
                console.error('Failed to load personas:', error);
            }
        }

        function renderPersonaSelector() {
            const container = document.getElementById('personaSelector');
            container.innerHTML = '';

            availablePersonas.forEach(persona => {
                const card = document.createElement('div');
                card.className = 'persona-card';
                card.setAttribute('data-persona-id', persona.persona_id);
                card.setAttribute('data-voice-style', persona.voice_style || 'warm');
                card.innerHTML = `
                    <div class="persona-name">${persona.name}</div>
                    <div class="persona-description">${persona.description}</div>
                `;
                card.addEventListener('click', () => selectPersona(persona.persona_id));
                container.appendChild(card);
            });
        }

        function selectPersona(personaId) {
            // Update UI
            document.querySelectorAll('.persona-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-persona-id="${personaId}"]`).classList.add('selected');

            // Update form data
            formData.selectedPersonaId = personaId;
            
            // Update persona validation status
            validatePersonaSelection();
            
            saveFormData();
        }

        function validatePersonaSelection() {
            const selectedPersonaCard = document.querySelector('.persona-card.selected');
            const personaSelector = document.getElementById('personaSelector');
            
            // Clear previous validation messages
            const existingMessage = personaSelector.parentNode.querySelector('.field-validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            // Create validation message element
            const validationMessage = document.createElement('div');
            validationMessage.className = 'field-validation-message';
            validationMessage.style.marginTop = '8px';
            
            if (selectedPersonaCard) {
                validationMessage.className += ' field-success-message';
                validationMessage.textContent = '✓ 人设选择完成';
                validationMessage.style.color = '#16a34a';
            } else {
                validationMessage.className += ' field-error-message';
                validationMessage.textContent = '请选择一个人设模板';
                validationMessage.style.color = '#dc2626';
            }
            
            personaSelector.parentNode.insertBefore(validationMessage, personaSelector.nextSibling);
        }

        function showSection(sectionNumber) {
            // Hide all sections
            for (let i = 1; i <= 6; i++) {
                document.getElementById(`section${i}`).classList.add('hidden');
            }

            // Show target section
            document.getElementById(`section${sectionNumber}`).classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('.section-nav button').forEach((btn, index) => {
                btn.classList.remove('active');
                if (index + 1 === sectionNumber) {
                    btn.classList.add('active');
                }
            });

            currentSection = sectionNumber;

            // Special handling for section 4 (persona configuration)
            if (sectionNumber === 4) {
                // Validate persona selection when entering the section
                setTimeout(() => {
                    validatePersonaSelection();
                }, 100); // Small delay to ensure DOM is updated
            }
            
            // Special handling for section 6 (validation)
            if (sectionNumber === 6) {
                validateForm();
            }

            updateProgress();
        }

        function nextSection() {
            if (currentSection < 6) {
                showSection(currentSection + 1);
            }
        }

        function prevSection() {
            if (currentSection > 1) {
                showSection(currentSection - 1);
            }
        }

        function addSellingPoint() {
            sellingPointsCount++;
            const container = document.getElementById('sellingPointsList');
            
            const sellingPoint = document.createElement('div');
            sellingPoint.className = 'selling-point';
            sellingPoint.innerHTML = `
                <div class="selling-point-header">
                    <div class="selling-point-title">卖点 #${sellingPointsCount}</div>
                    <select class="priority-badge" data-point-id="${sellingPointsCount}">
                        <option value="high">高优先级</option>
                        <option value="medium" selected>中优先级</option>
                        <option value="low">低优先级</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>卖点标题</label>
                    <input type="text" data-field="title" data-point-id="${sellingPointsCount}" placeholder="简洁描述这个卖点">
                </div>
                
                <div class="form-group">
                    <label>详细描述</label>
                    <textarea data-field="description" data-point-id="${sellingPointsCount}" placeholder="详细说明这个卖点的内容和价值"></textarea>
                </div>
                
                <div class="form-group">
                    <label>支撑事实（每行一个）</label>
                    <textarea data-field="facts" data-point-id="${sellingPointsCount}" placeholder="列出支撑这个卖点的具体事实和数据"></textarea>
                </div>
                
            `;
            
            container.appendChild(sellingPoint);
            
            // No auto-save - users must manually save using the save buttons
        }

        function parseDetailedErrorMessage(errorDetail) {
            /**
             * Parse detailed error response and format for user display
             * @param {Object} errorDetail - Error detail from API response
             * @returns {string} Formatted error message
             */
            let message = '保存失败：';

            // Check for field-specific errors
            if (errorDetail.field_errors && errorDetail.field_errors.length > 0) {
                message += '\n\n具体错误：';
                errorDetail.field_errors.forEach((fieldError, index) => {
                    message += `\n${index + 1}. ${fieldError.user_message}`;
                });

                // Add general guidance
                message += '\n\n请修正上述错误后重新保存。';
            } else if (errorDetail.details) {
                // Fallback to details field
                message += `\n${errorDetail.details}`;
            } else if (errorDetail.message) {
                // Fallback to message field
                message += `\n${errorDetail.message}`;
            } else if (typeof errorDetail === 'string') {
                // Fallback to string representation
                message += `\n${errorDetail}`;
            } else {
                // Ultimate fallback
                message += '\n数据验证错误，请检查输入内容';
            }

            return message;
        }

        function highlightFieldErrors(fieldErrors) {
            /**
             * Highlight form fields that have validation errors
             * @param {Array} fieldErrors - Array of field error objects
             */
            // Clear previous error highlights
            clearFieldErrorHighlights();

            if (!fieldErrors || fieldErrors.length === 0) {
                return;
            }

            fieldErrors.forEach(fieldError => {
                const fieldPath = fieldError.field_path;
                if (!fieldPath) return;

                // Try to find the corresponding form field
                const fieldElement = findFormFieldByPath(fieldPath);
                if (fieldElement) {
                    // Add error styling
                    fieldElement.classList.add('field-error');

                    // Add error message tooltip or adjacent text
                    addFieldErrorMessage(fieldElement, fieldError.user_message);
                }
            });
        }

        function clearFieldErrorHighlights() {
            /**
             * Clear all field error highlights and messages
             */
            const errorFields = document.querySelectorAll('.field-error');
            errorFields.forEach(field => {
                field.classList.remove('field-error');
            });

            const errorMessages = document.querySelectorAll('.field-error-message');
            errorMessages.forEach(msg => {
                msg.remove();
            });
        }

        function findFormFieldByPath(fieldPath) {
            /**
             * Find form field element by field path
             * @param {string} fieldPath - Dot-separated field path
             * @returns {Element|null} Form field element
             */
            const parts = fieldPath.split('.');
            const mainField = parts[0];

            // Map field names to form elements
            const fieldMappings = {
                'stream_title': 'input[name="stream_title"]',
                'current_price': 'input[name="current_price"]',
                'original_price': 'input[name="original_price"]',
                'primary_sku': 'input[name="primary_sku"]',
                'product_name': 'input[name="product_name"]',
                'brand': 'input[name="brand"]',
                'category': 'input[name="category"]',
                'key_specifications': 'textarea[name="key_specifications"]',
                'primary_value_proposition': 'textarea[name="primary_value_proposition"]',
                'call_to_actions': 'textarea[name="call_to_actions"]'
            };

            const selector = fieldMappings[mainField];
            if (selector) {
                return document.querySelector(selector);
            }

            // Handle selling points array fields
            if (mainField === 'selling_points' && parts.length > 1) {
                const index = parts[1];
                const subField = parts[2];

                if (subField) {
                    const sellingPointSelector = `[data-field="${subField}"][data-point-id="${index}"]`;
                    return document.querySelector(sellingPointSelector);
                }
            }

            return null;
        }

        function addFieldErrorMessage(fieldElement, errorMessage) {
            /**
             * Add error message near the field element
             * @param {Element} fieldElement - Form field element
             * @param {string} errorMessage - Error message to display
             */
            // Remove existing error message for this field
            const existingError = fieldElement.parentNode.querySelector('.field-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Create error message element
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error-message';
            errorDiv.style.color = '#e74c3c';
            errorDiv.style.fontSize = '12px';
            errorDiv.style.marginTop = '4px';
            errorDiv.textContent = errorMessage;

            // Insert after the field element
            fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
        }

        function validateSectionData(sectionNumber, sectionData) {
            const errors = [];
            
            try {
                switch (sectionNumber) {
                    case 1: // Basic Information
                        if (!sectionData.stream_title || sectionData.stream_title.trim().length < 10) {
                            errors.push('直播标题必须至少10个字符');
                        }
                        if (!sectionData.stream_type) {
                            errors.push('请选择直播类型');
                        }
                        if (!sectionData.planned_duration_minutes || sectionData.planned_duration_minutes < 30 || sectionData.planned_duration_minutes > 180) {
                            errors.push('直播时长必须在30-180分钟之间');
                        }
                        break;
                        
                    case 2: // Product Information
                        if (!sectionData.primary_sku || sectionData.primary_sku.trim().length < 3) {
                            errors.push('商品SKU必须至少3个字符');
                        }
                        if (!sectionData.product_name || sectionData.product_name.trim().length < 5) {
                            errors.push('商品名称必须至少5个字符');
                        }
                        if (!sectionData.brand || sectionData.brand.trim().length < 2) {
                            errors.push('品牌名称必须至少2个字符');
                        }
                        if (!sectionData.category || sectionData.category.trim().length < 3) {
                            errors.push('商品分类必须至少3个字符');
                        }
                        if (!sectionData.current_price || sectionData.current_price <= 0) {
                            errors.push('商品价格必须大于0');
                        }
                        if (!sectionData.key_specifications || sectionData.key_specifications.length === 0) {
                            errors.push('至少需要添加一个商品规格');
                        }
                        break;
                        
                    case 3: // Selling Points Structure
                        if (!sectionData.primary_value_proposition || sectionData.primary_value_proposition.trim().length < 20) {
                            errors.push('核心价值主张必须至少20个字符');
                        }
                        if (!sectionData.selling_points || sectionData.selling_points.length < 3) {
                            errors.push('至少需要添加3个卖点');
                        }
                        // Validate each selling point
                        sectionData.selling_points.forEach((point, index) => {
                            if (!point.title || point.title.trim().length < 5) {
                                errors.push(`第${index + 1}个卖点标题至少5个字符`);
                            }
                            if (!point.description || point.description.trim().length < 10) {
                                errors.push(`第${index + 1}个卖点描述至少10个字符`);
                            }
                            if (!point.supporting_facts || point.supporting_facts.length < 1) {
                                errors.push(`第${index + 1}个卖点至少需要1个支撑事实`);
                            }
                        });
                        if (!sectionData.competitive_advantages || sectionData.competitive_advantages.length < 1) {
                            errors.push('至少需要添加1个竞争优势');
                        }
                        if (!sectionData.call_to_actions || sectionData.call_to_actions.length < 2) {
                            errors.push('至少需要添加2个行动号召');
                        }
                        break;
                        
                    case 4: // Persona Configuration
                        if (!sectionData.selected_persona_id || sectionData.selected_persona_id.trim() === '') {
                            errors.push('请选择一个有效的人设');
                        }
                        if (!sectionData.persona_name || sectionData.persona_name.trim().length < 2) {
                            errors.push('人设名称不能为空');
                        }
                        break;
                        
                    case 5: // Advanced Settings
                        // Advanced settings are mostly optional, basic validation
                        if (sectionData.words_per_minute_override && (sectionData.words_per_minute_override < 100 || sectionData.words_per_minute_override > 300)) {
                            errors.push('语速设置必须在100-300之间');
                        }
                        break;
                        
                    case 6: // Review and Validation
                        // Section 6 validation is handled by the API
                        break;
                        
                    default:
                        errors.push('无效的节数');
                }
            } catch (e) {
                errors.push(`数据验证过程中出错: ${e.message}`);
            }
            
            return errors;
        }

        // Add save throttling to prevent rapid successive saves
        let saveInProgress = false;
        let lastSaveTime = 0;
        const MIN_SAVE_INTERVAL = 1000; // Minimum 1 second between saves

        async function saveFormData(quickSave = false) {
            // Check if save is already in progress
            if (saveInProgress) {
                console.log('Save already in progress, skipping...');
                return;
            }

            // Throttle rapid successive saves
            const currentTime = Date.now();
            if (currentTime - lastSaveTime < MIN_SAVE_INTERVAL) {
                console.log('Save throttled, too frequent saves');
                return;
            }

            saveInProgress = true;
            lastSaveTime = currentTime;

            try {
                // Collect form data from current section
                collectFormData();

                if (!formId) {
                    console.error('No form ID available for saving');
                    showNotification('保存失败：表单ID缺失', 'error');
                    return;
                }
                // Extract section-specific data based on current section
                let sectionData = null;
                const sectionKeyMap = {
                    1: 'basic_information',
                    2: 'product_information', 
                    3: 'selling_points_structure',
                    4: 'persona_configuration',
                    5: 'advanced_settings',
                    6: 'review_and_validation'
                };

                const sectionKey = sectionKeyMap[currentSection];
                if (!sectionKey || !formData[sectionKey]) {
                    throw new Error(`第${currentSection}节数据不存在或格式错误`);
                }

                sectionData = formData[sectionKey];

                // Comprehensive client-side validation before API call (skip for quick saves)
                if (!quickSave) {
                    const validationErrors = validateSectionData(currentSection, sectionData);
                    if (validationErrors.length > 0) {
                        const errorMessage = '数据验证失败：\\n' + validationErrors.join('\\n');
                        console.error('Validation errors:', validationErrors);
                        showNotification(errorMessage, 'error');
                        updateSaveStatus('error', '保存失败：数据验证错误');
                        return;
                    }
                } else {
                    console.log('Quick save mode: skipping comprehensive validation');
                }

                // Show saving indicator
                updateSaveStatus('saving', '正在保存第' + currentSection + '节数据...');

                const response = await fetch(`/api/operational/forms/${formId}/section/${currentSection}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(sectionData)  // Send only section-specific data
                });

                if (response.ok) {
                    const result = await response.json();
                    updateProgress(result.completion_percentage);
                    
                    updateSaveStatus('saved', '第' + currentSection + '节数据已保存');
                    
                    // Update script readiness status after successful save
                    addManagedTimeout(() => updateScriptReadinessStatus(), 500);
                } else {
                    // Handle API error responses with detailed field-level error parsing
                    let errorData = {};
                    let detailedErrorMessage = '保存失败：数据验证错误';

                    try {
                        errorData = await response.json();

                        // Parse structured error response
                        if (errorData.detail) {
                            detailedErrorMessage = parseDetailedErrorMessage(errorData.detail);

                            // Highlight field errors if available
                            if (errorData.detail.field_errors) {
                                highlightFieldErrors(errorData.detail.field_errors);
                            }
                        }
                    } catch (e) {
                        console.error('Failed to parse error response:', e);
                    }

                    console.error('API Error Details:', {
                        status: response.status,
                        statusText: response.statusText,
                        errorData: errorData,
                        sectionData: sectionData,
                        currentSection: currentSection,
                        detailedErrorMessage: detailedErrorMessage
                    });
                    
                    // Handle both new standardized and legacy error formats
                    let errorMessage;
                    if (errorData.detail) {
                        if (typeof errorData.detail === 'object' && errorData.detail.message) {
                            // New standardized error format
                            errorMessage = errorData.detail.message;
                            if (errorData.detail.details) {
                                errorMessage += ` - ${errorData.detail.details}`;
                            }
                        } else {
                            // Legacy error format
                            errorMessage = errorData.detail;
                        }
                    } else {
                        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                    }
                    throw new Error(`${errorMessage}`);
                }
            } catch (error) {
                console.error('Save error:', error);
                updateSaveStatus('error', `保存失败: ${error.message}`);
                throw error; // Re-throw for manual error handling
            } finally {
                // Always reset the save flag
                saveInProgress = false;
            }
        }

        function collectFormData() {
            // Collect data from the current section based on section number
            
            if (currentSection === 1) {
                // Section 1: Basic Information
                formData.basic_information = {
                    stream_title: document.getElementById('streamTitle').value,
                    stream_type: document.getElementById('streamType').value,
                    planned_duration_minutes: parseInt(document.getElementById('plannedDuration').value),
                    target_timezone: document.getElementById('targetTimezone').value,
                    enable_auto_interaction: document.getElementById('enableAutoInteraction')?.checked || false
                };
            }
            
            else if (currentSection === 2) {
                // Section 2: Product Information
                formData.product_information = {
                    primary_sku: document.getElementById('primarySku').value,
                    product_name: document.getElementById('productName').value,
                    brand: document.getElementById('brand').value,
                    category: document.getElementById('category').value,
                    current_price: parseFloat(document.getElementById('currentPrice').value) || 0,
                    original_price: parseFloat(document.getElementById('originalPrice').value) || null,
                    key_specifications: document.getElementById('keySpecifications').value.split('\n').filter(spec => spec.trim())
                };
            }
            
            else if (currentSection === 3) {
                // Section 3: Selling Points Configuration
                const sellingPoints = [];
                const sellingPointElements = document.querySelectorAll('#sellingPointsList .selling-point');
                sellingPointElements.forEach((element, index) => {
                    const pointText = element.querySelector('textarea')?.value;
                    const priority = element.querySelector('.priority-badge')?.value;
                    
                    // Collect supporting facts from facts-list
                    const factsContainer = element.querySelector('.facts-list');
                    const supporting_facts = [];
                    if (factsContainer) {
                        const factInputs = factsContainer.querySelectorAll('input[type="text"]');
                        factInputs.forEach(input => {
                            if (input.value && input.value.trim()) {
                                supporting_facts.push(input.value.trim());
                            }
                        });
                    }
                    
                    if (pointText && pointText.trim()) {
                        // Extract title from first line or first sentence, ensure minimum length
                        let title = pointText.trim().split('\n')[0] || pointText.trim().split('。')[0] || pointText.trim();

                        // Ensure title is at least 5 characters and at most 100 characters
                        if (title.length < 5) {
                            title = pointText.trim().substring(0, Math.min(50, pointText.trim().length));
                        }
                        if (title.length > 100) {
                            title = title.substring(0, 97) + '...';
                        }

                        // Create proper SellingPoint structure matching API expectations
                        sellingPoints.push({
                            point_id: `user_point_${index + 1}`,
                            title: title,
                            description: pointText.trim(),
                            priority: priority || 'medium',
                            supporting_facts: supporting_facts
                        });
                    }
                });
                
                formData.selling_points_structure = {
                    primary_value_proposition: document.getElementById('valueProposition').value,
                    selling_points: sellingPoints,
                    competitive_advantages: document.getElementById('competitiveAdvantages').value.split('\n').filter(adv => adv.trim()),
                    call_to_actions: document.getElementById('callToActions').value.split('\n').filter(cta => cta.trim())
                };
            }
            
            else if (currentSection === 4) {
                // Section 4: Persona Configuration
                const selectedPersonaCard = document.querySelector('.persona-card.selected');
                let selectedPersonaId, selectedPersonaName, selectedVoiceStyle;
                
                if (selectedPersonaCard) {
                    selectedPersonaId = selectedPersonaCard.getAttribute('data-persona-id');
                    selectedPersonaName = selectedPersonaCard.querySelector('.persona-name')?.textContent || '';
                    selectedVoiceStyle = selectedPersonaCard.getAttribute('data-voice-style') || 'warm';
                } else {
                    // No persona selected, use first available persona as default
                    const firstPersona = availablePersonas[0];
                    if (firstPersona) {
                        selectedPersonaId = firstPersona.persona_id;
                        selectedPersonaName = firstPersona.name;
                        selectedVoiceStyle = firstPersona.voice_style || 'warm';
                    } else {
                        // Fallback if no personas are available
                        selectedPersonaId = 'energetic_host';
                        selectedPersonaName = 'Energetic Host';
                        selectedVoiceStyle = 'warm';
                    }
                }
                
                formData.persona_configuration = {
                    selected_persona_id: selectedPersonaId,
                    persona_name: selectedPersonaName,
                    voice_style: selectedVoiceStyle,
                    speaking_rate: parseFloat(document.getElementById('speakingRate').value) || 1.0,
                    energy_level: document.getElementById('energyLevel').value || 'medium',
                    custom_greetings: document.getElementById('customGreetings').value.split('\n').filter(greeting => greeting.trim())
                };
            }
            
            else if (currentSection === 5) {
                // Section 5: Advanced Settings
                formData.advanced_settings = {
                    words_per_minute: parseInt(document.getElementById('wordsPerMinute').value) || null,
                    question_timeout: parseInt(document.getElementById('questionTimeout').value) || 300,
                    enable_fact_checking: document.getElementById('enableFactChecking')?.checked || false,
                    // Add other advanced settings as needed
                };
            }
            
            else if (currentSection === 6) {
                // Section 6: Review and Validation - collect proper ReviewAndValidation structure
                formData.review_and_validation = {
                    content_validation_rules: [],
                    compliance_validation_rules: [],
                    technical_validation_rules: [],
                    content_review_completed: false,
                    compliance_review_completed: false,
                    technical_review_completed: false,
                    approved_by: null,
                    approved_at: null,
                    approval_notes: null,
                    ready_for_submission: false,
                    submission_blocked_reasons: []
                };
            }
        }

        async function validateForm() {
            try {
                document.getElementById('validationSummary').innerHTML = `
                    <div class="validation-title">配置验证中...</div>
                    <div class="loading"><div class="spinner"></div></div>
                `;

                const response = await fetch(`/api/operational/forms/${formId}/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        validation_type: 'complete'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    displayValidationResults(result);
                }
            } catch (error) {
                console.error('Validation error:', error);
                showNotification('验证过程出错', 'error');
            }
        }

        function displayValidationResults(result) {
            const container = document.getElementById('validationSummary');
            const submitBtn = document.getElementById('submitBtn');

            if (result.is_valid) {
                // Check if validation was skipped
                if (result.validation_errors && result.validation_errors.length === 0 && result.validation_mode !== "validation_only") {
                    container.innerHTML = `
                        <div class="validation-title" style="color: var(--warning-color);">⚠ 配置验证已跳过</div>
                        <ul class="validation-list warning">
                            <li>严格验证已在配置中禁用</li>
                            <li>表单将直接接受当前内容</li>
                            <li>建议开发环境使用，生产环境请启用验证</li>
                        </ul>
                    `;
                    container.className = 'validation-summary warning';
                } else {
                    container.innerHTML = `
                        <div class="validation-title" style="color: var(--success-color);">✓ 配置验证通过</div>
                        <ul class="validation-list success">
                            <li>所有必填项已完成</li>
                            <li>数据格式正确</li>
                            <li>内容符合规范</li>
                        </ul>
                    `;
                    container.className = 'validation-summary success';
                }
                submitBtn.disabled = false;
            } else {
                container.innerHTML = `
                    <div class="validation-title" style="color: var(--error-color);">✗ 配置验证失败</div>
                    <ul class="validation-list">
                        ${result.validation_errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                `;
                container.className = 'validation-summary';
                submitBtn.disabled = true;
            }
        }

        async function submitForm() {
            try {
                showNotification('正在提交配置...', 'info');

                const response = await fetch(`/api/operational/forms/${formId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showNotification('配置提交成功！系统将开始处理', 'success');
                    document.getElementById('submitBtn').textContent = '已提交';
                    document.getElementById('submitBtn').disabled = true;
                    
                    // 显示表单ID和操作选项
                    showFormIdInfo(formId);
                } else {
                    const error = await response.json();
                    throw new Error(error.detail.message || 'Submission failed');
                }
            } catch (error) {
                console.error('Submission error:', error);
                showNotification('提交失败: ' + error.message, 'error');
            }
        }

        function updateProgress(percentage) {
            if (percentage === undefined) {
                // Calculate based on current section
                percentage = (currentSection / 6) * 100;
            }
            
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = `完成度: ${Math.round(percentage)}%`;
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            addManagedTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }



        async function saveCurrentSection() {
            // Manual save for current section
            try {
                updateSaveStatus('saving', '正在保存第' + currentSection + '节数据...');
                await saveFormData();
                showNotification('当前节保存成功', 'success');
            } catch (error) {
                console.error('Manual save error:', error);
                showNotification(`保存失败: ${error.message}`, 'error');
                updateSaveStatus('error', `保存失败: ${error.message}`);
            }
        }

        async function saveAllSections() {
            // Save all sections with data
            updateSaveStatus('saving', '正在保存全部数据...');
            
            try {
                let savedCount = 0;
                const totalSections = 6;
                
                for (let sectionNum = 1; sectionNum <= totalSections; sectionNum++) {
                    const tempCurrentSection = currentSection;
                    currentSection = sectionNum;
                    
                    try {
                        collectFormData();
                        
                        const sectionKeyMap = {
                            1: 'basic_information',
                            2: 'product_information', 
                            3: 'selling_points_structure',
                            4: 'persona_configuration',
                            5: 'advanced_settings',
                            6: 'review_and_validation'
                        };

                        const sectionKey = sectionKeyMap[sectionNum];
                        if (formData[sectionKey]) {
                            const response = await fetch(`/api/operational/forms/${formId}/section/${sectionNum}`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(formData[sectionKey])
                            });

                            if (response.ok) {
                                savedCount++;
                            }
                        }
                    } catch (error) {
                        console.error(`保存第${sectionNum}节失败:`, error);
                    }
                    
                    currentSection = tempCurrentSection;
                }
                
                if (savedCount > 0) {
                    updateSaveStatus('saved', `已保存 ${savedCount}/${totalSections} 个节`);
                } else {
                    updateSaveStatus('error', '没有数据可保存');
                }
                
            } catch (error) {
                console.error('批量保存失败:', error);
                updateSaveStatus('error', '批量保存失败');
            }
        }

        async function getFormValidationStatus(formIdToCheck) {
            try {
                const response = await fetch(`/api/operational/forms/${formIdToCheck}/validation`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    return await response.json();
                }
                return null;
            } catch (error) {
                console.error('Form validation status error:', error);
                return null;
            }
        }

        async function validateFormExists(formIdToCheck) {
            try {
                const response = await fetch(`/api/operational/forms/${formIdToCheck}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const form = await response.json();
                    return form && form.form_id === formIdToCheck;
                }
                return false;
            } catch (error) {
                console.error('Form validation error:', error);
                return false;
            }
        }

        async function updateScriptReadinessStatus() {
            if (!formId) return;
            
            try {
                const validation = await getFormValidationStatus(formId);
                if (!validation) return;
                
                const readiness = document.getElementById('scriptReadiness');
                const readinessIcon = document.getElementById('readinessIcon');
                const readinessText = document.getElementById('readinessText');
                const readinessDetails = document.getElementById('readinessDetails');
                
                // Remove existing classes
                readiness.className = 'script-readiness';
                readiness.style.display = 'block';
                
                if (validation.is_ready_for_script) {
                    readiness.classList.add('ready');
                    readinessIcon.textContent = '✅';
                    readinessText.textContent = '脚本生成就绪';
                    readinessDetails.textContent = `完成度: ${validation.completion_percentage.toFixed(1)}% | 质量分: ${validation.data_quality_score.toFixed(1)}%`;
                } else if (validation.has_template_data) {
                    readiness.classList.add('template-data');
                    readinessIcon.textContent = '⚠️';
                    readinessText.textContent = '包含模板数据';
                    readinessDetails.textContent = `需要完善: ${validation.template_fields.join(', ')}`;
                } else {
                    readiness.classList.add('not-ready');
                    readinessIcon.textContent = '📝';
                    readinessText.textContent = '需要更多内容';
                    readinessDetails.textContent = `完成度: ${validation.completion_percentage.toFixed(1)}%`;
                }
                
                // Auto-hide after 10 seconds if not ready
                if (!validation.is_ready_for_script) {
                    setTimeout(() => {
                        readiness.style.display = 'none';
                    }, 10000);
                }
                
            } catch (error) {
                console.error('Error updating script readiness status:', error);
            }
        }

        function updateSaveStatus(status, message = '') {
            const saveStatus = document.getElementById('saveStatus');
            const saveStatusIcon = document.getElementById('saveStatusIcon');
            const saveStatusText = document.getElementById('saveStatusText');
            const saveTimestamp = document.getElementById('saveTimestamp');

            // Remove existing status classes
            saveStatus.className = 'save-status';
            
            // Show the status indicator
            saveStatus.style.display = 'block';

            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            switch(status) {
                case 'saving':
                    saveStatus.classList.add('saving');
                    saveStatusIcon.textContent = '⏳';
                    saveStatusText.textContent = message || '正在保存...';
                    saveTimestamp.textContent = '';
                    break;
                
                case 'saved':
                    saveStatus.classList.add('saved');
                    saveStatusIcon.textContent = '✅';
                    saveStatusText.textContent = message || '已保存';
                    saveTimestamp.textContent = `于 ${timeString}`;
                    // Auto-hide after 5 seconds
                    setTimeout(() => {
                        saveStatus.style.display = 'none';
                    }, 5000);
                    break;
                
                case 'error':
                    saveStatus.classList.add('error');
                    saveStatusIcon.textContent = '❌';
                    saveStatusText.textContent = message || '保存失败';
                    saveTimestamp.textContent = `于 ${timeString}`;
                    // Auto-hide after 8 seconds
                    setTimeout(() => {
                        saveStatus.style.display = 'none';
                    }, 8000);
                    break;
                
                case 'hide':
                    saveStatus.style.display = 'none';
                    break;
            }
        }

        function showFormIdInfo(formId) {
            const formIdInfo = document.getElementById('formIdInfo');
            const formIdDisplay = document.getElementById('formIdDisplay');
            
            formIdDisplay.value = formId;
            formIdInfo.style.display = 'block';
            
            // 滚动到表单ID显示区域
            formIdInfo.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        function copyFormId() {
            const formIdDisplay = document.getElementById('formIdDisplay');
            formIdDisplay.select();
            formIdDisplay.setSelectionRange(0, 99999); // For mobile devices
            
            try {
                document.execCommand('copy');
                showNotification('表单ID已复制到剪贴板', 'success');
            } catch (err) {
                console.error('复制失败:', err);
                showNotification('复制失败，请手动选择复制', 'error');
            }
        }

        async function generateScript() {
            const formId = document.getElementById('formIdDisplay').value;
            if (!formId) {
                showNotification('表单ID不存在', 'error');
                return;
            }
            
            try {
                showNotification('正在生成脚本，请稍候...', 'info');
                
                const response = await fetch('/api/script-preview/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ form_id: formId, regenerate: false })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showNotification('脚本生成成功！', 'success');
                    
                    // 询问用户是否要查看脚本
                    if (confirm('脚本生成成功！是否现在查看脚本？')) {
                        window.open(`/api/script-preview/view/${formId}`, '_blank');
                    }
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || 'Script generation failed');
                }
            } catch (error) {
                console.error('Script generation error:', error);
                showNotification('脚本生成失败: ' + error.message, 'error');
            }
        }

        // Initialize with first selling point (only for new forms)
        setTimeout(() => {
            const urlParams = new URLSearchParams(window.location.search);
            const formIdFromUrl = urlParams.get('form_id');
            if (!formIdFromUrl) {
                // Only add empty selling point for new forms
                addSellingPoint();
            }
        }, 100);

        // Load existing form data for editing
        async function loadExistingFormData(formId) {
            try {
                showNotification('正在加载表单数据...', 'info');
                
                const response = await fetch(`/api/operational/forms/${formId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const form = await response.json();
                    
                    // Validate form data exists
                    if (!form || !form.basic_information) {
                        throw new Error('表单数据不完整或已损坏');
                    }
                    
                    // Set global form data (formId already set in main initialization)  
                    formData = form;
                    
                    // Load basic information (Section 1) - 修正字段ID匹配
                    if (form.basic_information) {
                        const basic = form.basic_information;
                        document.getElementById('streamTitle').value = basic.stream_title || '';
                        document.getElementById('streamType').value = basic.stream_type || 'daily_stream';
                        document.getElementById('plannedDuration').value = basic.planned_duration_minutes || 90;
                        document.getElementById('targetTimezone').value = basic.target_timezone || 'Asia/Tokyo';
                        if (document.getElementById('enableAutoInteraction')) {
                            document.getElementById('enableAutoInteraction').checked = basic.enable_auto_interaction || false;
                        }
                    }
                    
                    // Load product information (Section 2) - 修正字段ID匹配
                    if (form.product_information) {
                        const product = form.product_information;
                        document.getElementById('primarySku').value = product.primary_sku || '';
                        document.getElementById('productName').value = product.product_name || '';
                        document.getElementById('brand').value = product.brand || '';
                        document.getElementById('category').value = product.category || '';
                        document.getElementById('currentPrice').value = product.current_price || '';
                        if (document.getElementById('originalPrice')) {
                            document.getElementById('originalPrice').value = product.original_price || '';
                        }
                        if (document.getElementById('keySpecifications')) {
                            document.getElementById('keySpecifications').value = (product.key_specifications || []).join('\n');
                        }
                        
                        // Load specifications
                        if (product.key_specifications && product.key_specifications.length > 0) {
                            const specsContainer = document.getElementById('specifications-list');
                            specsContainer.innerHTML = '';
                            product.key_specifications.forEach(spec => {
                                addSpecificationField(spec);
                            });
                        }
                    }
                    
                    // Load selling points (Section 3) - 修正字段ID匹配
                    if (form.selling_points_structure) {
                        const selling = form.selling_points_structure;
                        if (document.getElementById('valueProposition')) {
                            document.getElementById('valueProposition').value = selling.primary_value_proposition || '';
                        }
                        
                        // Load competitive advantages
                        if (selling.competitive_advantages && selling.competitive_advantages.length > 0) {
                            const advantagesContainer = document.getElementById('advantages-list');
                            advantagesContainer.innerHTML = '';
                            selling.competitive_advantages.forEach(advantage => {
                                addAdvantageField(advantage);
                            });
                        }
                        
                        // Load selling points
                        if (selling.selling_points && selling.selling_points.length > 0) {
                            const sellingPointsContainer = document.getElementById('sellingPointsList');
                            sellingPointsContainer.innerHTML = '';
                            sellingPointsCount = 0;
                            selling.selling_points.forEach(point => {
                                addSellingPointWithData(point);
                            });
                        }
                        
                        // Load call-to-actions
                        if (selling.call_to_actions && selling.call_to_actions.length > 0) {
                            const ctaContainer = document.getElementById('cta-list');
                            ctaContainer.innerHTML = '';
                            selling.call_to_actions.forEach(cta => {
                                addCtaField(cta);
                            });
                        }
                    }
                    
                    // Load persona configuration (Section 4) - 修正字段ID匹配
                    if (form.persona_configuration) {
                        const persona = form.persona_configuration;
                        
                        // Restore persona selection
                        if (persona.selected_persona_id && persona.selected_persona_id !== 'default') {
                            const personaCard = document.querySelector(`[data-persona-id="${persona.selected_persona_id}"]`);
                            if (personaCard) {
                                personaCard.classList.add('selected');
                            }
                        }
                        
                        // Restore other persona settings
                        if (document.getElementById('speakingRate')) {
                            document.getElementById('speakingRate').value = persona.speaking_rate || 1.0;
                            const rateDisplay = document.getElementById('speakingRateValue');
                            if (rateDisplay) rateDisplay.textContent = persona.speaking_rate || 1.0;
                        }
                        if (document.getElementById('energyLevel')) {
                            document.getElementById('energyLevel').value = persona.energy_level || 'medium';
                        }
                        if (document.getElementById('customGreetings')) {
                            document.getElementById('customGreetings').value = (persona.custom_greetings || []).join('\n');
                        }
                    }
                    
                    // Update progress and UI
                    updateProgressBar();
                    showNotification('表单数据加载成功', 'success');
                    
                    // Check script generation readiness
                    addManagedTimeout(() => updateScriptReadinessStatus(), 1000);
                    
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    // Handle both new standardized and legacy error formats
                    let errorMessage;
                    if (errorData.detail) {
                        if (typeof errorData.detail === 'object' && errorData.detail.message) {
                            errorMessage = errorData.detail.message;
                            if (errorData.detail.details) {
                                errorMessage += ` - ${errorData.detail.details}`;
                            }
                        } else {
                            errorMessage = errorData.detail;
                        }
                    } else {
                        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                    }
                    throw new Error(`加载表单失败: ${errorMessage}`);
                }
                
            } catch (error) {
                console.error('Load form data error:', error);
                showNotification('表单数据加载失败: ' + error.message, 'error');
                
                // If form doesn't exist, offer to create new one
                if (error.message.includes('404') || error.message.includes('不存在')) {
                    const shouldCreateNew = confirm('指定的表单不存在，是否创建新表单？');
                    if (shouldCreateNew) {
                        // Remove form_id from URL and reload
                        const url = new URL(window.location);
                        url.searchParams.delete('form_id');
                        window.location.href = url.toString();
                    }
                }
            }
        }
        
        // Helper functions for loading existing data
        function addSpecificationField(value = '') {
            const html = `
                <div class="spec-item">
                    <input type="text" value="${value}" placeholder="输入规格参数">
                    <button type="button" onclick="removeSpecification(this)">删除</button>
                </div>
            `;
            document.getElementById('specifications-list').insertAdjacentHTML('beforeend', html);
        }
        
        function addAdvantageField(value = '') {
            const html = `
                <div class="advantage-item">
                    <input type="text" value="${value}" placeholder="输入竞争优势">
                    <button type="button" onclick="removeAdvantage(this)">删除</button>
                </div>
            `;
            document.getElementById('advantages-list').insertAdjacentHTML('beforeend', html);
        }
        
        function addCtaField(value = '') {
            const html = `
                <div class="cta-item">
                    <input type="text" value="${value}" placeholder="输入行动召唤">
                    <button type="button" onclick="removeCta(this)">删除</button>
                </div>
            `;
            document.getElementById('cta-list').insertAdjacentHTML('beforeend', html);
        }

        // Helper function to add selling point with existing data
        function addSellingPointWithData(pointData) {
            sellingPointsCount++;
            const pointId = pointData.point_id || `point_${sellingPointsCount}`;
            
            const html = `
                <div class="selling-point-item" id="point_${sellingPointsCount}">
                    <div class="selling-point-header">
                        <span class="point-number">${sellingPointsCount}</span>
                        <button type="button" onclick="removeSellingPoint(${sellingPointsCount})" class="btn btn-danger btn-sm">删除</button>
                    </div>
                    <div class="form-group">
                        <label>卖点标题 *</label>
                        <input type="text" class="selling-point-title" name="selling_point_title_${sellingPointsCount}" data-field="selling_point_title" data-point-index="${sellingPointsCount}" value="${pointData.title || ''}" required>
                    </div>
                    <div class="form-group">
                        <label>详细描述 *</label>
                        <textarea class="selling-point-description" name="selling_point_description_${sellingPointsCount}" data-field="selling_point_description" data-point-index="${sellingPointsCount}" rows="3" required>${pointData.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label>优先级</label>
                        <select class="selling-point-priority">
                            <option value="high" ${pointData.priority === 'high' ? 'selected' : ''}>高</option>
                            <option value="medium" ${pointData.priority === 'medium' ? 'selected' : ''}>中</option>
                            <option value="low" ${pointData.priority === 'low' ? 'selected' : ''}>低</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>支撑事实</label>
                        <div class="facts-list" id="facts_${sellingPointsCount}">
                            ${pointData.supporting_facts ? pointData.supporting_facts.map((fact, factIndex) => 
                                `<div class="fact-item">
                                    <input type="text" class="supporting-fact-input" data-field="supporting_facts" data-point-index="${sellingPointsCount}" data-fact-index="${factIndex}" value="${fact}" placeholder="输入支撑事实">
                                    <button type="button" onclick="removeFact(this)">删除</button>
                                </div>`
                            ).join('') : ''}
                        </div>
                        <button type="button" onclick="addFact(${sellingPointsCount})" class="btn btn-secondary btn-sm">添加事实</button>
                    </div>
                </div>
            `;
            
            document.getElementById('sellingPointsList').insertAdjacentHTML('beforeend', html);
            
            // Add real-time validation to newly added selling point fields
            const newPointElement = document.getElementById(`point_${sellingPointsCount}`);
            const titleInput = newPointElement.querySelector('.selling-point-title');
            const descriptionTextarea = newPointElement.querySelector('.selling-point-description');
            const factInputs = newPointElement.querySelectorAll('.supporting-fact-input');
            
            if (titleInput) addSingleFieldValidation(titleInput);
            if (descriptionTextarea) addSingleFieldValidation(descriptionTextarea);
            factInputs.forEach(input => addSingleFieldValidation(input));
            
            // Update selling points count validation
            validateSellingPointsCount();
        }

        // Real-time validation functions
        function validateFieldRealTime(field) {
            /**
             * Perform real-time validation on a field
             * @param {Element} field - Form field element
             */
            const fieldName = field.name || field.getAttribute('data-field');
            const fieldValue = field.value;

            // Clear previous validation state
            clearFieldValidation(field);

            // Perform field-specific validation
            const validationResult = validateSingleField(fieldName, fieldValue, field);

            if (!validationResult.isValid) {
                showFieldValidationError(field, validationResult.message);
            } else {
                showFieldValidationSuccess(field);
            }
        }

        function validateSingleField(fieldName, value, fieldElement) {
            /**
             * Validate a single field value
             * @param {string} fieldName - Name of the field
             * @param {string} value - Field value
             * @param {Element} fieldElement - Field DOM element
             * @returns {Object} Validation result
             */
            switch (fieldName) {
                case 'stream_title':
                    if (!value.trim()) {
                        return { isValid: false, message: '直播标题不能为空' };
                    }
                    if (value.trim().length < 10) {
                        return { isValid: false, message: '直播标题至少需要10个字符' };
                    }
                    break;

                case 'current_price':
                    if (!value) {
                        return { isValid: false, message: '当前价格不能为空' };
                    }
                    const price = parseFloat(value);
                    if (isNaN(price) || price <= 0) {
                        return { isValid: false, message: '当前价格必须大于0' };
                    }

                    // Check against original price if exists
                    const originalPriceField = document.querySelector('input[name="original_price"]');
                    if (originalPriceField && originalPriceField.value) {
                        const originalPrice = parseFloat(originalPriceField.value);
                        if (!isNaN(originalPrice) && price >= originalPrice) {
                            return { isValid: false, message: '当前价格必须低于原价' };
                        }
                    }
                    break;

                case 'original_price':
                    if (value) {
                        const originalPrice = parseFloat(value);
                        if (isNaN(originalPrice) || originalPrice <= 0) {
                            return { isValid: false, message: '原价必须大于0' };
                        }

                        // Check against current price
                        const currentPriceField = document.querySelector('input[name="current_price"]');
                        if (currentPriceField && currentPriceField.value) {
                            const currentPrice = parseFloat(currentPriceField.value);
                            if (!isNaN(currentPrice) && originalPrice <= currentPrice) {
                                return { isValid: false, message: '原价必须高于当前价格' };
                            }
                        }
                    }
                    break;

                case 'primary_sku':
                    if (!value.trim()) {
                        return { isValid: false, message: '主要SKU不能为空' };
                    }
                    if (['TBD', 'TODO', 'TEMP'].includes(value.trim().toUpperCase())) {
                        return { isValid: false, message: 'SKU不能是占位符文本' };
                    }
                    break;

                case 'product_name':
                    if (!value.trim()) {
                        return { isValid: false, message: '产品名称不能为空' };
                    }
                    if (value.trim().length < 5) {
                        return { isValid: false, message: '产品名称至少需要5个字符' };
                    }
                    if (value.startsWith('待')) {
                        return { isValid: false, message: '产品名称不能包含占位符文本' };
                    }
                    break;

                case 'brand':
                    if (!value.trim()) {
                        return { isValid: false, message: '品牌名称不能为空' };
                    }
                    if (value.trim().length < 2) {
                        return { isValid: false, message: '品牌名称至少需要2个字符' };
                    }
                    break;

                case 'category':
                    if (!value.trim()) {
                        return { isValid: false, message: '商品分类不能为空' };
                    }
                    if (value.trim().length < 3) {
                        return { isValid: false, message: '商品分类至少需要3个字符' };
                    }
                    break;

                case 'key_specifications':
                    if (fieldElement.tagName === 'TEXTAREA') {
                        const specs = value.split('\n').filter(spec => spec.trim());
                        if (specs.length === 0) {
                            return { isValid: false, message: '至少需要1个产品规格' };
                        }
                        if (specs.length > 10) {
                            return { isValid: false, message: '产品规格不能超过10个' };
                        }
                    }
                    break;

                case 'call_to_actions':
                    if (fieldElement.tagName === 'TEXTAREA') {
                        const ctas = value.split('\n').filter(cta => cta.trim());
                        if (ctas.length < 2) {
                            return { isValid: false, message: '至少需要2个行动号召' };
                        }
                        if (ctas.length > 5) {
                            return { isValid: false, message: '行动号召不能超过5个' };
                        }
                    }
                    break;

                case 'primary_value_proposition':
                    if (!value.trim()) {
                        return { isValid: false, message: '主要价值主张不能为空' };
                    }
                    if (value.trim().length < 20) {
                        return { isValid: false, message: '主要价值主张至少需要20个字符' };
                    }
                    break;

                case 'selling_point_title':
                    if (!value.trim()) {
                        return { isValid: false, message: '卖点标题不能为空' };
                    }
                    if (value.trim().length < 5) {
                        return { isValid: false, message: '卖点标题至少需要5个字符' };
                    }
                    if (value.trim().length > 100) {
                        return { isValid: false, message: '卖点标题不能超过100个字符' };
                    }
                    break;

                case 'selling_point_description':
                    if (!value.trim()) {
                        return { isValid: false, message: '卖点描述不能为空' };
                    }
                    if (value.trim().length < 10) {
                        return { isValid: false, message: '卖点描述至少需要10个字符' };
                    }
                    if (value.trim().length > 500) {
                        return { isValid: false, message: '卖点描述不能超过500个字符' };
                    }
                    break;

                case 'supporting_facts':
                    if (!value.trim()) {
                        return { isValid: false, message: '支撑事实不能为空' };
                    }
                    if (value.trim().length < 3) {
                        return { isValid: false, message: '支撑事实至少需要3个字符' };
                    }
                    break;
            }

            return { isValid: true, message: '' };
        }

        function clearFieldValidation(field) {
            /**
             * Clear validation state for a field
             * @param {Element} field - Form field element
             */
            field.classList.remove('field-error', 'field-success');

            const existingMessage = field.parentNode.querySelector('.field-validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }
        }

        function showFieldValidationError(field, message) {
            /**
             * Show validation error for a field
             * @param {Element} field - Form field element
             * @param {string} message - Error message
             */
            field.classList.add('field-error');
            field.classList.remove('field-success');

            const messageDiv = document.createElement('div');
            messageDiv.className = 'field-validation-message field-error-message';
            messageDiv.textContent = message;

            field.parentNode.insertBefore(messageDiv, field.nextSibling);
        }

        function showFieldValidationSuccess(field) {
            /**
             * Show validation success for a field
             * @param {Element} field - Form field element
             */
            field.classList.add('field-success');
            field.classList.remove('field-error');

            const messageDiv = document.createElement('div');
            messageDiv.className = 'field-validation-message field-success-message';
            messageDiv.textContent = '✓';
            messageDiv.style.color = '#27ae60';
            messageDiv.style.fontSize = '12px';
            messageDiv.style.marginTop = '4px';

            field.parentNode.insertBefore(messageDiv, field.nextSibling);
        }

        // Functions for managing supporting facts
        function addFact(pointIndex) {
            const factsContainer = document.getElementById(`facts_${pointIndex}`);
            const factIndex = factsContainer.children.length;
            
            const factHtml = `
                <div class="fact-item">
                    <input type="text" class="supporting-fact-input" data-field="supporting_facts" data-point-index="${pointIndex}" data-fact-index="${factIndex}" placeholder="输入支撑事实">
                    <button type="button" onclick="removeFact(this)">删除</button>
                </div>
            `;
            
            factsContainer.insertAdjacentHTML('beforeend', factHtml);
            
            // Add real-time validation to the new input
            const newInput = factsContainer.lastElementChild.querySelector('input');
            addSingleFieldValidation(newInput);
        }

        function removeFact(button) {
            const factItem = button.closest('.fact-item');
            if (factItem) {
                factItem.remove();
            }
        }

        function removeSellingPoint(pointId) {
            const pointElement = document.getElementById(`point_${pointId}`);
            if (pointElement) {
                pointElement.remove();
                // Update selling points count validation after removal
                validateSellingPointsCount();
            }
        }

        // Validate selling points count and show feedback
        function validateSellingPointsCount() {
            const sellingPointsList = document.getElementById('sellingPointsList');
            const sellingPoints = sellingPointsList.querySelectorAll('.selling-point-item');
            const count = sellingPoints.length;
            
            // Find or create validation message element
            let validationMessage = document.getElementById('selling-points-count-validation');
            if (!validationMessage) {
                validationMessage = document.createElement('div');
                validationMessage.id = 'selling-points-count-validation';
                validationMessage.className = 'field-validation-message';
                validationMessage.style.marginTop = '8px';
                sellingPointsList.parentNode.insertBefore(validationMessage, sellingPointsList.nextSibling);
            }
            
            if (count < 3) {
                validationMessage.className = 'field-validation-message field-error-message';
                validationMessage.textContent = `需要至少3个卖点，当前只有${count}个`;
                validationMessage.style.color = '#dc2626';
            } else {
                validationMessage.className = 'field-validation-message field-success-message';
                validationMessage.textContent = `✓ 卖点数量符合要求 (${count}/3+)`;
                validationMessage.style.color = '#16a34a';
            }
        }

        // Add real-time validation to a single field
        function addSingleFieldValidation(field) {
            // Delayed validation on input
            field.addEventListener('input', function() {
                clearTimeout(this.validationTimeout);
                this.validationTimeout = setTimeout(() => {
                    validateFieldRealTime(this);
                }, 500); // 500ms delay
            });

            // Immediate validation on blur
            field.addEventListener('blur', function() {
                clearTimeout(this.validationTimeout);
                validateFieldRealTime(this);
            });

            // Clear validation state on focus
            field.addEventListener('focus', function() {
                if (this.classList.contains('field-error')) {
                    clearFieldValidation(this);
                }
            });
        }

        // Add event listeners to clear field errors when user starts typing
        function addFieldErrorClearListeners() {
            const formFields = document.querySelectorAll('input, textarea, select');
            formFields.forEach(field => {
                // Real-time validation on input
                field.addEventListener('input', function() {
                    // Debounce validation to avoid excessive calls
                    clearTimeout(this.validationTimeout);
                    this.validationTimeout = setTimeout(() => {
                        validateFieldRealTime(this);
                    }, 500); // 500ms delay
                });

                // Immediate validation on blur
                field.addEventListener('blur', function() {
                    clearTimeout(this.validationTimeout);
                    validateFieldRealTime(this);
                });

                // Clear validation state on focus
                field.addEventListener('focus', function() {
                    if (this.classList.contains('field-error')) {
                        clearFieldValidation(this);
                    }
                });
            });
        }

        // Initialize field error clearing when page loads
        document.addEventListener('DOMContentLoaded', function() {
            addFieldErrorClearListeners();
            // Initialize selling points count validation if on section 3
            const currentSection = document.querySelector('.section-content.active');
            if (currentSection && currentSection.querySelector('#sellingPointsList')) {
                validateSellingPointsCount();
            }

            // Initialize character count
            const valueProposition = document.getElementById('valueProposition');
            if (valueProposition) {
                updateCharacterCount('valueProposition', 'valuePropositionCount', 20, 300);
            }
        });

        // Character count function for validation feedback
        function updateCharacterCount(inputId, countId, minLength, maxLength) {
            const input = document.getElementById(inputId);
            const countElement = document.getElementById(countId);

            if (input && countElement) {
                const currentLength = input.value.length;
                let status = '';
                let className = '';

                if (currentLength < minLength) {
                    status = `${currentLength}/${minLength} (还需${minLength - currentLength}个字符)`;
                    className = 'char-count-insufficient';
                } else if (currentLength > maxLength) {
                    status = `${currentLength}/${maxLength} (超出${currentLength - maxLength}个字符)`;
                    className = 'char-count-exceeded';
                } else {
                    status = `${currentLength}/${maxLength} ✓`;
                    className = 'char-count-valid';
                }

                countElement.textContent = status;
                countElement.className = `char-count ${className}`;
            }
        }

    </script>
</body>
</html>