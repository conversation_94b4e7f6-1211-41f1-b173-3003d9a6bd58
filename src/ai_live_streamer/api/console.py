"""
Console API Endpoints

REST API for the admin console to manage forms, scripts, and system monitoring.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from loguru import logger

from ..models.forms import OperationalForm

# Router setup
console_router = APIRouter(prefix="/api/console", tags=["console"])

# Import existing storage from operational_forms
from .operational_forms import form_storage
# Import script_preview module to access its functions and data
from . import script_preview


@console_router.get("/stats", response_model=Dict[str, Any])
async def get_dashboard_stats() -> Dict[str, Any]:
    """Get dashboard statistics"""
    try:
        form_stats = form_storage.get_form_stats()
        total_forms = form_stats.get('total_forms', 0)
        # Get script count from script_preview service
        script_list_response = await script_preview.list_script_previews()
        total_scripts = script_list_response.get('total_previews', 0)
        
        # System status (simplified)
        system_status = "运行中"
        
        # Recent activity
        recent_forms_data = form_storage.list_forms(limit=5)
        recent_forms = []
        for form_data in recent_forms_data:
            recent_forms.append({
                "form_id": form_data['id'],
                "title": form_data['id'][:8] + "...",  # Use form ID prefix as title since we only have metadata
                "created_at": form_data['created_at'].isoformat() if form_data.get('created_at') else None,
                "is_processed": form_data.get('is_processed', False)
            })
        
        return {
            "total_forms": total_forms,
            "total_scripts": total_scripts,
            "system_status": system_status,
            "recent_forms": recent_forms,
            "last_updated": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get dashboard stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get stats: {str(e)}"
        )


@console_router.get("/forms", response_model=Dict[str, Any])
async def get_forms_list() -> Dict[str, Any]:
    """Get list of all forms"""
    try:
        forms_list = []
        
        forms_data = form_storage.list_forms(limit=100)
        for form_data in forms_data:
            forms_list.append({
                "form_id": form_data['id'],
                "title": form_data['id'][:8] + "...",  # Use form ID prefix since we only have metadata
                "stream_type": "unknown",  # Would need full form data to get this
                "product_name": "unknown",  # Would need full form data to get this
                "created_at": form_data['created_at'].isoformat() if form_data.get('created_at') else None,
                "last_modified": form_data['last_modified_at'].isoformat() if form_data.get('last_modified_at') else None,
                "is_submitted": form_data.get('is_submitted', False),
                "is_processed": form_data.get('is_processed', False),
                "completion_percentage": form_data.get('completion_percentage', 0),
                "status": "completed" if form_data.get('is_processed') else "draft"
            })
        
        # Sort by last modified time (newest first)
        forms_list.sort(key=lambda x: x["last_modified"] or "", reverse=True)
        
        return {
            "forms": forms_list,
            "total_count": len(forms_list),
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get forms list: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get forms: {str(e)}"
        )


@console_router.get("/scripts", response_model=Dict[str, Any])
async def get_scripts_list() -> Dict[str, Any]:
    """Get list of all generated scripts by calling the script_preview service"""
    try:
        scripts_list = []
        
        # Directly call the list_script_previews function from the script_preview module
        try:
            db_response = await script_preview.list_script_previews()
            
            # Process database previews
            for db_preview in db_response.get("previews", []):
                form_id = db_preview.get("form_id")
                
                # Get corresponding form info if available
                form_title = db_preview.get("title", "Unknown Form")
                product_name = db_preview.get("product_name", "")
                company_name = db_preview.get("company_name", "")
                
                # Try to get more detailed info from form storage
                try:
                    form = form_storage.get_form(form_id)
                    if form:
                        form_title = form.basic_information.stream_title
                        product_name = form.product_information.product_name
                        company_name = form.basic_information.stream_title
                except Exception:
                    pass  # Keep info from database if form not found
                
                # Build preview content from outline
                outline = db_preview.get("outline", [])
                preview_content = "\n".join(outline[:3]) if outline else "No preview available"
                if len(outline) > 3:
                    preview_content += f"\n... 还有 {len(outline) - 3} 个段落"
                
                scripts_list.append({
                    "form_id": form_id,
                    "form_title": form_title,
                    "product_name": product_name,
                    "company_name": company_name,
                    "preview_content": preview_content,
                    "total_segments": db_preview.get("segments_count", 0),
                    "total_duration_minutes": db_preview.get("estimated_duration_minutes", 0),
                    "generation_time": db_preview.get("generation_time_seconds", 0),
                    "warnings": [],
                    "estimated_words": 0,  # Not available in current db structure
                    "generated_at": db_preview.get("created_at", datetime.utcnow().isoformat())
                })
                
        except Exception as e:
            logger.error(f"Failed to get scripts from database: {e}")
            # Re-raise the exception since we don't have a fallback
            raise
        
        # Sort by generated_at (most recent first)
        scripts_list.sort(key=lambda x: x["generated_at"], reverse=True)
        
        return {
            "scripts": scripts_list,
            "total_count": len(scripts_list),
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get scripts list: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scripts: {str(e)}"
        )


@console_router.delete("/forms/{form_id}")
async def delete_form(form_id: str) -> Dict[str, Any]:
    """Delete a form and its associated script"""
    try:
        form = form_storage.get_form(form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {form_id}"
            )
        
        # Delete from forms storage
        form_storage.delete_form(form_id)
        
        # Delete associated script using the script_preview service
        try:
            await script_preview.clear_preview_cache(form_id)
        except Exception as e:
            logger.warning(f"Failed to delete script preview for form {form_id}: {e}")
        
        logger.info(f"Deleted form {form_id} and its associated script.")
        
        return {
            "status": "success",
            "message": f"Form {form_id} deleted successfully",
            "deleted_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete form {form_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete form: {str(e)}"
        )


@console_router.delete("/scripts/{form_id}")
async def delete_script(form_id: str) -> Dict[str, Any]:
    """Delete a generated script using the script_preview service"""
    try:
        # Directly call the delete function from the script_preview module
        delete_response = await script_preview.clear_preview_cache(form_id)
        
        if not delete_response.get("success"):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Script not found for form: {form_id}"
            )
        
        logger.info(f"Deleted script for form {form_id}")
        
        return {
            "status": "success",
            "message": f"Script for form {form_id} deleted successfully",
            "deleted_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete script for form {form_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete script: {str(e)}"
        )


@console_router.get("/system/health")
async def get_system_health() -> Dict[str, Any]:
    """Get system health information"""
    try:
        return {
            "status": "healthy",
            "uptime": "running",
            "forms_count": form_storage.get_form_stats().get('total_forms', 0),
            "scripts_count": (await script_preview.list_script_previews()).get('total_previews', 0),
            "memory_usage": "N/A",  # Could add actual memory monitoring
            "last_check": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get system health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system health: {str(e)}"
        )