"""FastAPI application for AI Live Streamer system

Main application setup with all routers, middleware, and configuration
for the operational input system and control interfaces.
"""

from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from contextlib import asynccontextmanager
from pathlib import Path
import time
import asyncio
import logging
from loguru import logger

from .operational_forms import operational_router
from .control import router as control_router
from .script_preview import script_preview_router  # 🔄 Restored script preview functionality
from .auth import auth_router
# V1 audio_websocket removed - using V2 WebSocket
# from .audio_websocket import audio_ws_router
from .websocket_routes_v2 import router as websocket_v2_router  # V2 WebSocket routes
# audio_control router removed as part of architecture simplification
from .monitoring import monitoring_router
from .status_websocket import router as status_ws_router
from .system_status import router as system_status_router  # 🔄 Restored system status API
from ..core.config import cfg
from ..core.exceptions import ServiceError




def configure_logging():
    """Configure logging to filter out HTTP access logs"""
    # 配置uvicorn访问日志过滤器
    class AccessLogFilter(logging.Filter):
        def filter(self, record):
            # 过滤掉特定的HTTP访问日志
            if hasattr(record, 'getMessage'):
                message = record.getMessage()
                # 过滤掉 /api/status 和 /api/control/question-queue 的访问日志
                if any(endpoint in message for endpoint in [
                    'GET /api/status',
                    'GET /api/control/question-queue'
                ]):
                    return False
            return True

    # 应用过滤器到uvicorn的访问日志记录器
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.addFilter(AccessLogFilter())


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Simplified application lifespan manager"""
    # Startup
    logger.info("🚀 Starting AI Live Streamer API server (simplified architecture)")

    # Configure logging
    configure_logging()
    logger.info("🔧 Logging configuration applied")

    # Initialize basic services
    try:
        logger.info("🏭 Initializing core services")

        # V2: Services are initialized through dependencies.py
        # But we still need to initialize the TTS engine
        logger.info("✅ Using V2 dependency injection system")
        
        # Initialize TTS engine for V2 system
        from ..core.dependencies import set_tts_engine
        from ..services.factories import ServiceFactory
        
        logger.info("🎵 Initializing TTS engine...")
        tts_engine = await ServiceFactory.create_tts_engine()
        set_tts_engine(tts_engine)
        logger.info(f"✅ TTS engine initialized: {type(tts_engine).__name__}")
        
        # Start up V2 components
        from ..core.dependencies import startup_components
        await startup_components()
        logger.info("✅ V2 core components started")

    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        raise ServiceError(f"Service initialization failed: {e}", "app_startup", "SERVICE_INIT_FAILED")
    
    logger.info("✅ Simplified architecture startup completed")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down AI Live Streamer API server")
    
    # Basic cleanup
    try:
        # V2: Cleanup handled by dependencies.py shutdown
        from ..core.dependencies import shutdown_components
        await shutdown_components()
        logger.info("✅ V2 services cleanup completed")
        
    except Exception as e:
        logger.warning(f"⚠️ Error during cleanup: {e}")
    
    logger.info("✅ Simplified architecture shutdown completed")


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="AI Live Streamer API",
        description="AI驱动的直播解说系统 - 运营配置和控制接口",
        version="0.1.0",
        docs_url="/docs" if cfg.debug else None,
        redoc_url="/redoc" if cfg.debug else None,
        lifespan=lifespan
    )
    
    # Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Request timing middleware
    @app.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response
    
    # Include routers
    app.include_router(operational_router)
    app.include_router(control_router)  # 🚀 Unified control API with new architecture
    app.include_router(script_preview_router)  # 🔄 Restored script preview API
    app.include_router(auth_router)
    # V1 audio_ws_router removed - using V2 WebSocket
    # app.include_router(audio_ws_router)
    app.include_router(websocket_v2_router)  # 🎯 V2 WebSocket for client-driven streaming
    app.include_router(status_ws_router)  # 📡 WebSocket status broadcasting
    app.include_router(system_status_router)  # 🔄 Restored system status API for frontend compatibility
    # audio_control router removed as part of architecture simplification
    app.include_router(monitoring_router)  # 📊 New monitoring and health check API
    
    # 🚀 Phase 2.4: QA性能监控API
    from .qa_monitoring import qa_monitoring_router
    app.include_router(qa_monitoring_router)  # 🎯 QA插入性能监控和分析端点
    
    # Include console router
    from .console import console_router
    app.include_router(console_router)
    
    # Static files for frontend
    templates_dir = Path(__file__).parent / "templates"
    static_dir = templates_dir / "static"
    logger.debug(f"🔍 Checking for static directory at: {static_dir.resolve()}")
    if static_dir.exists():
        logger.info(f"✅ Static directory found. Mounting static files from: {static_dir}")
        app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    else:
        logger.error(f"❌ Static directory NOT FOUND at expected path: {static_dir.resolve()}")
        logger.warning("⚠️ Static files will not be served. Frontend may not work correctly.")
    
    # Admin Console route
    @app.get("/", response_class=HTMLResponse)
    async def serve_admin_console():
        """Serve the admin console"""
        html_file = templates_dir / "admin_console.html"
        if html_file.exists():
            return HTMLResponse(content=html_file.read_text(encoding='utf-8'))
        else:
            return HTMLResponse(
                content="<h1>AI Live Streamer</h1><p>Admin Console not found</p>",
                status_code=404
            )
    
    # Operational form route
    @app.get("/config", response_class=HTMLResponse)
    async def serve_config_form():
        """Serve the operational form frontend"""
        html_file = templates_dir / "operational_form.html"
        if html_file.exists():
            return HTMLResponse(content=html_file.read_text(encoding='utf-8'))
        else:
            return HTMLResponse(
                content="<h1>AI Live Streamer</h1><p>Configuration form not found</p>",
                status_code=404
            )
    
    # Live control panel route
    @app.get("/control", response_class=HTMLResponse)
    async def serve_live_control_panel():
        """Serve the live control panel frontend"""
        html_file = templates_dir / "live_control_panel.html"
        if html_file.exists():
            return HTMLResponse(content=html_file.read_text(encoding='utf-8'))
        else:
            return HTMLResponse(
                content="<h1>AI Live Streamer</h1><p>Live control panel not found</p>",
                status_code=404
            )
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "version": "0.1.0",
            "environment": cfg.app_env
        }
    
    # Global exception handler
    @app.exception_handler(ServiceError)
    async def service_error_handler(request: Request, exc: ServiceError):
        """Handle service errors"""
        logger.error(f"Service error in {request.url}: {exc}")
        return {
            "error": "service_error",
            "message": str(exc),
            "service_name": exc.service_name,
            "error_code": exc.error_code
        }
    
    # Generic exception handler
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected errors"""
        logger.error(f"Unexpected error in {request.url}: {exc}")
        return {
            "error": "internal_error", 
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    
    logger.info("FastAPI application created successfully")
    return app


# Create app instance
app = create_app()