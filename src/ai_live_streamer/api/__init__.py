"""API layer for AI Live Streamer system

Provides REST endpoints for operational input, control, and monitoring
of the live streaming system.
"""

from .app import create_app
from .operational_forms import operational_router
from .control import router as control_router
# status router removed as part of architecture simplification

__all__ = [
    "create_app",
    "operational_router",
    "control_router",
]