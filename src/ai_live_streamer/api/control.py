"""Control API endpoints for live stream management

Provides endpoints for controlling the live streaming system using the new
LiveStreamController architecture with full dependency injection support.
Supports both static and dynamic content generation modes.
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field
from loguru import logger

from ..models.content import Question, QuestionPriority
from ..core.exceptions import ServiceError, TimeoutError
# V1 imports removed - using V2 architecture
# from ..core.live_stream_controller import LiveStreamController, IMainContentPlayer, IQAManager
# from ..core.live_stream_session import LiveStreamSession
from ..core.script_generator_v2 import ScriptGenerator, SmartScriptGenerator, GenerationConfig
# V2: Using V2 dependency injection instead of V1 app_config
# from ..core.app_config import (...) - REMOVED
from ..services.factories import ServiceFactory

async def broadcast_state_update(state_data: Dict[str, Any]):
    """广播状态更新到所有连接的客户端（使用StateBroadcaster架构解耦）"""
    try:
        # 获取统一的状态广播器
        from ..core.dependencies import get_state_broadcaster
        state_broadcaster = get_state_broadcaster()
        
        # 构造状态更新消息
        message = {
            "type": "state_update",
            "timestamp": datetime.utcnow().isoformat(),
            "data": state_data
        }
        
        # 📡 使用架构解耦的广播器进行广播
        success_count = await state_broadcaster.broadcast_state_update(message)
        logger.info(f"状态更新已广播到 {success_count} 个客户端")
        return success_count
    except Exception as e:
        logger.error(f"广播状态更新失败: {e}")
        return 0


# Request/Response models
class StreamMode(str, Enum):
    """Stream operation mode"""
    STATIC = "static"
    DYNAMIC = "dynamic"
    SMART = "smart"  # Smart preloading mode
    SCRIPT_BASED = "script_based"  # Script-based streaming from operational forms


class StartStreamRequest(BaseModel):
    """Request model for starting a new stream"""
    mode: StreamMode = Field(default=StreamMode.STATIC, description="Stream operation mode")
    content: Optional[List[str]] = Field(None, description="Static content for static mode")
    topic: Optional[str] = Field(None, description="Initial topic for dynamic generation")
    persona_id: Optional[str] = Field(None, description="Persona ID for the stream")
    session_id: Optional[str] = Field(None, description="Custom session ID")
    generation_config: Optional[Dict[str, Any]] = Field(None, description="Config for dynamic generation")
    # New fields for script-based streaming
    form_id: Optional[str] = Field(None, description="Form ID for script-based streaming")
    prepared_stream_id: Optional[str] = Field(None, description="Prepared stream data ID")
    user_id: Optional[str] = Field(None, description="User performing the operation")


class QuestionSubmitRequest(BaseModel):
    """Request model for submitting questions"""
    text: str = Field(..., description="The question text")
    viewer_name: Optional[str] = Field(None, description="Name of the viewer asking")
    priority: QuestionPriority = Field(default=QuestionPriority.MEDIUM, description="Question priority")
    sku_id: Optional[str] = Field(None, description="Product SKU if question is product-related")
    product_name: Optional[str] = Field(None, description="Product name if applicable")


class StreamControlRequest(BaseModel):
    """Request model for stream control operations"""
    reason: Optional[str] = Field(None, description="Reason for the operation")
    user_id: Optional[str] = Field(None, description="User performing the operation")


# Router setup
router = APIRouter(prefix="/api/control", tags=["control"])

# Session registry - V2 uses playlist manager instead of controllers
_active_sessions: Dict[str, Dict[str, Any]] = {}


# Dependency injection helpers
async def get_session_info(
    session_id: str,
    error_if_not_found: bool = True
) -> Optional[Dict[str, Any]]:
    """Get session info by session ID"""
    session_info = _active_sessions.get(session_id)
    if not session_info and error_if_not_found:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session {session_id} not found"
        )
    return session_info


async def get_default_session() -> Optional[Dict[str, Any]]:
    """Get the default/main session"""
    return await get_session_info("main", error_if_not_found=False)


def get_active_sessions() -> Dict[str, Any]:
    """Get information about all active sessions (for monitoring compatibility)
    
    Returns session information in a format compatible with the monitoring API.
    """
    sessions_info = {}
    for session_id, session_data in _active_sessions.items():
        try:
            # Get basic session info
            sessions_info[session_id] = {
                "session_id": session_id,
                "mode": session_data.get("mode", "unknown"),
                "is_running": session_data.get("is_running", True),
                "started_at": session_data.get("started_at")
            }
        except Exception as e:
            logger.warning(f"Failed to get info for session {session_id}: {str(e)}")
            sessions_info[session_id] = {
                "session_id": session_id,
                "error": str(e)
            }
    
    return sessions_info


def create_session_dependency(session_id: str):
    """Create a dependency function for getting session info by session ID"""
    async def session_dep() -> Dict[str, Any]:
        return await get_session_info(session_id)
    return session_dep


# API Endpoints
@router.post("/start-stream")
async def start_stream(
    request: StartStreamRequest
) -> Dict[str, Any]:
    """Start a new live stream with specified mode and content
    
    Supports four modes:
    - static: Play predefined content list
    - dynamic: Generate content on-the-fly based on topic  
    - smart: Dynamic generation with intelligent preloading
    - script_based: Play structured script from operational forms (NEW!)
    """
    try:
        # Validate request
        if request.mode == StreamMode.STATIC and not request.content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Static mode requires content list"
            )
        
        if request.mode in [StreamMode.DYNAMIC, StreamMode.SMART] and not request.topic:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Dynamic mode requires initial topic"
            )
        
        if request.mode == StreamMode.SCRIPT_BASED and not (request.form_id or request.prepared_stream_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Script-based mode requires form_id or prepared_stream_id"
            )
        
        # Use V2 dependencies
        from ..core.dependencies import (
            get_playlist_manager,
            get_client_state_tracker,
            get_content_provider
        )
        
        # For V2, we don't use the old controller/player pattern
        # Instead, use the V2 playlist-based system
        playlist_manager = get_playlist_manager()
        client_tracker = get_client_state_tracker()
        content_provider = get_content_provider()
        
        # Create session ID
        import uuid
        session_id = request.session_id or f"session_{int(datetime.utcnow().timestamp())}_{uuid.uuid4().hex[:8]}"
        
        # Create ready event for synchronization
        ready_event = asyncio.Event()
        
        # V2: Initialize session data early with status tracking
        session_data = {
            "session_id": session_id,
            "mode": request.mode.value,
            "status": "initializing",  # Track initialization status
            "ready_event": ready_event,  # For WebSocket synchronization
            "is_running": True,
            "started_at": datetime.utcnow().isoformat(),
            "created_by": request.user_id or "api",
            "is_paused": False,
            "playlist_info": None  # Will be populated after initialization
        }
        
        # CRITICAL: Register session IMMEDIATELY to prevent race condition
        # This ensures WebSocket connections can find the session even during initialization
        _active_sessions[session_id] = session_data
        logger.info(f"📝 Session {session_id} registered with status: initializing")
        
        # Prepare content based on mode
        if request.mode == StreamMode.STATIC:
            # Static content mode
            content = request.content
            logger.info(f"🔍 STATIC mode content received: {len(content) if content else 0} items")
            
        elif request.mode == StreamMode.SCRIPT_BASED:
            # Script-based content mode
            logger.info(f"🎬 SCRIPT_BASED mode - processing form_id: {request.form_id}, prepared_stream_id: {request.prepared_stream_id}")
            
            try:
                # Import the converter
                from ..services.script_to_content_converter import convert_prepared_stream_to_content
                
                # Get prepared stream data from script preview cache
                if request.prepared_stream_id:
                    from .script_preview import preview_cache
                    prepared_data = preview_cache.get(request.prepared_stream_id)
                    
                    if not prepared_data:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail=f"Prepared stream data not found: {request.prepared_stream_id}"
                        )
                    
                    # Convert to content list
                    content = convert_prepared_stream_to_content(prepared_data)
                    
                elif request.form_id:
                    # First check if we have a cached preview to avoid regeneration
                    from .script_preview import get_preview_from_cache_or_db, get_form_by_id, script_previewer
                    
                    logger.info(f"🔍 Checking for cached preview for form {request.form_id}")
                    cached_preview = get_preview_from_cache_or_db(request.form_id)
                    
                    if cached_preview and cached_preview.success and cached_preview.script_timeline:
                        # Use cached preview - avoid regeneration
                        logger.info(f"✅ Using cached preview for form {request.form_id} - skipping regeneration")
                        preview_result = cached_preview
                    else:
                        # No cache found, generate script from form
                        logger.info(f"📝 No cached preview found for form {request.form_id}, generating new script")
                        
                        form = get_form_by_id(request.form_id)
                        if not form:
                            raise HTTPException(
                                status_code=status.HTTP_404_NOT_FOUND,
                                detail=f"Form not found: {request.form_id}"
                            )
                        
                        # Generate script preview
                        logger.info(f"🎭 Generating script for form {request.form_id}")
                        preview_result = await script_previewer.generate_script_preview(form)
                        
                        if not preview_result.success or not preview_result.script_timeline:
                            raise HTTPException(
                                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                detail=f"Script generation failed: {preview_result.error_messages}"
                            )
                    
                    # Convert to content list
                    from ..services.script_to_content_converter import get_default_converter
                    converter = get_default_converter()
                    content = converter.convert_timeline_to_content_list(preview_result.script_timeline)
                
                if not content or len(content) == 0:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="No content generated from script"
                    )
                
                # Validate that content is List[SentenceData]
                if content and hasattr(content[0], 'text'):
                    logger.info(f"✅ Script-based content prepared: {len(content)} SentenceData items")
                else:
                    logger.error(f"❌ Content type mismatch: expected SentenceData objects, got {type(content[0])}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Invalid content type from script converter"
                    )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ Script-based content preparation failed: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Script processing failed: {str(e)}"
                )
            
        else:
            # Dynamic content mode - create generator
            from ..core.dependencies import get_llm_adapter
            llm_adapter = await get_llm_adapter()
            
            # Load persona if specified (V2 doesn't use personas from app_config)
            persona = None
            # if request.persona_id:
            #     persona = app_config.get_persona(request.persona_id)
            
            # Create generation config
            gen_config = GenerationConfig(
                buffer_size=request.generation_config.get("buffer_size", 10) if request.generation_config else 10,
                preload_threshold=request.generation_config.get("preload_threshold", 3) if request.generation_config else 3,
                max_generation_time_sec=request.generation_config.get("max_generation_time_sec", 30.0) if request.generation_config else 30.0,
                context_window_size=request.generation_config.get("context_window_size", 5) if request.generation_config else 5
            )
            
            # Create appropriate generator
            if request.mode == StreamMode.SMART:
                generator = SmartScriptGenerator(
                    llm_adapter=llm_adapter,
                    sentence_splitter=None,  # Will use default
                    persona=persona,
                    config=gen_config
                )
            else:
                generator = ScriptGenerator(
                    llm_adapter=llm_adapter,
                    sentence_splitter=None,  # Will use default
                    persona=persona,
                    config=gen_config
                )
            
            # Create async generator for content
            content = generator.generate_stream(request.topic)
        
        # Initialize playlist based on mode
        if request.mode in [StreamMode.STATIC, StreamMode.SCRIPT_BASED]:
            # For STATIC mode, content is a list of strings that need conversion
            # For SCRIPT_BASED mode, content is already List[SentenceData] from converter
            if request.mode == StreamMode.STATIC:
                # Convert string list to SentenceData objects
                from ..models.playlist_models import SentenceData
                sentence_objects = []
                for i, text in enumerate(content):
                    sentence_data = SentenceData.from_string(text, index=i)
                    sentence_objects.append(sentence_data)
            else:
                # SCRIPT_BASED mode - content is already List[SentenceData]
                sentence_objects = content
            
            # Initialize playlist
            success = await playlist_manager.initialize_from_script(sentence_objects)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to initialize playlist"
                )
            
            logger.info(f"✅ Playlist initialized with {len(sentence_objects)} items for {request.mode} mode")
            
            # 触发主动预合成服务，预热前5个内容
            from ..core.dependencies import get_proactive_synthesizer
            proactive_synthesizer = get_proactive_synthesizer()
            if proactive_synthesizer and proactive_synthesizer._running:
                # 获取前5个播放项进行预合成
                playlist_items = []
                for i in range(min(5, len(sentence_objects))):
                    item = await playlist_manager.get_item_at(i)
                    if item:
                        playlist_items.append(item)
                
                if playlist_items:
                    logger.info(f"🔥 触发前{len(playlist_items)}个内容的预合成")
                    # 使用高优先级合成
                    await proactive_synthesizer.trigger_qa_synthesis(playlist_items)
            
            # V2架构：不再创建Controller，让客户端通过WebSocket拉取内容
            logger.info("✅ V2 mode: Playlist ready, waiting for client content requests")
            
        elif request.mode in [StreamMode.DYNAMIC, StreamMode.SMART]:
            # Dynamic mode: initialize playlist with generator
            # V2架构：同样不创建Controller，由客户端驱动内容生成
            logger.info(f"✅ V2 mode: Dynamic generator ready for topic: {request.topic}")
        
        # Update session status to ready and signal any waiting WebSocket connections
        session_data["status"] = "ready"
        if request.mode in [StreamMode.STATIC, StreamMode.SCRIPT_BASED]:
            # Store playlist info for verification
            session_data["playlist_info"] = await playlist_manager.get_playlist_info()
        
        # Signal that session is ready for WebSocket connections
        ready_event.set()
        logger.info(f"✅ Session {session_id} status updated to: ready")
        
        logger.info(f"🚀 Started {request.mode} stream for session {session_id}")
        
        response = {
            "status": "started",
            "session_id": session_id,
            "mode": request.mode,
            "started_at": datetime.utcnow().isoformat(),
            "topic": request.topic if request.mode not in [StreamMode.STATIC, StreamMode.SCRIPT_BASED] else None,
        }
        
        # Add mode-specific metadata
        if request.mode == StreamMode.STATIC:
            response["content_items"] = len(request.content) if request.content else 0
        elif request.mode == StreamMode.SCRIPT_BASED:
            response["form_id"] = request.form_id
            response["prepared_stream_id"] = request.prepared_stream_id
            response["script_content_items"] = len(content) if content else 0
        
        # Verify playlist initialization for static/script modes
        if request.mode in [StreamMode.STATIC, StreamMode.SCRIPT_BASED]:
            playlist_info = await playlist_manager.get_playlist_info()
            if playlist_info.total_items == 0:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Playlist validation failed - no items loaded for {request.mode}"
                )
            logger.info(f"✅ Playlist verified: {playlist_info.total_items} items, version {playlist_info.version}")
            response["playlist_items"] = playlist_info.total_items
        
        return response
        
    except HTTPException:
        # Clean up session on HTTP exception
        if 'session_id' in locals() and session_id in _active_sessions:
            del _active_sessions[session_id]
            if 'ready_event' in locals():
                ready_event.set()  # Unblock any waiting WebSocket connections
        raise
    except Exception as e:
        # Clean up session on any other exception
        if 'session_id' in locals() and session_id in _active_sessions:
            del _active_sessions[session_id]
            if 'ready_event' in locals():
                ready_event.set()  # Unblock any waiting WebSocket connections
        logger.error(f"Failed to start stream: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start stream: {str(e)}"
        )


# Legacy API endpoint removed as part of architecture simplification
# Use /start-stream endpoint instead


@router.post("/sessions/{session_id}/pause")
async def pause_stream(
    session_id: str,
    request: StreamControlRequest
) -> Dict[str, Any]:
    """Pause the specified streaming session"""
    try:
        session_info = await get_session_info(session_id)
        # V2: Pausing is handled by client state, not server controller
        session_info["is_paused"] = True
        
        logger.info(f"⏸️ Paused stream {session_id} by {request.user_id}: {request.reason}")
        
        return {
            "status": "paused",
            "session_id": session_id,
            "paused_at": datetime.utcnow().isoformat(),
            "reason": request.reason,
            "user_id": request.user_id
        }
        
    except Exception as e:
        logger.error(f"Failed to pause stream: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to pause stream: {str(e)}"
        )


@router.post("/sessions/{session_id}/resume")
async def resume_stream(
    session_id: str,
    request: StreamControlRequest
) -> Dict[str, Any]:
    """Resume the specified streaming session"""
    try:
        session_info = await get_session_info(session_id)
        # V2: Resuming is handled by client state, not server controller
        session_info["is_paused"] = False
        
        logger.info(f"▶️ Resumed stream {session_id} by {request.user_id}: {request.reason}")
        
        return {
            "status": "resumed",
            "session_id": session_id,
            "resumed_at": datetime.utcnow().isoformat(),
            "reason": request.reason,
            "user_id": request.user_id
        }
        
    except Exception as e:
        logger.error(f"Failed to resume stream: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resume stream: {str(e)}"
        )


@router.post("/sessions/{session_id}/stop")
async def stop_stream(
    session_id: str,
    request: StreamControlRequest
) -> Dict[str, Any]:
    """Stop the specified streaming session"""
    try:
        session_info = await get_session_info(session_id)
        if not session_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Session {session_id} not found"
            )
        
        # 停止控制器（如果存在）
        if "controller" in session_info:
            controller = session_info["controller"]
            # 对于静态内容控制器，停止播放流
            if hasattr(controller, 'stop_stream'):
                await controller.stop_stream()
                logger.info(f"Controller stopped for session {session_id}")
            # 对于动态控制器的其他停止逻辑
            elif hasattr(controller, 'stop'):
                await controller.stop()
        
        # V2: Mark session as stopped
        session_info["is_running"] = False
        session_info["stopped_at"] = datetime.utcnow().isoformat()
        
        # Remove from registry
        del _active_sessions[session_id]
        
        logger.info(f"⏹️ Stopped stream {session_id} by {request.user_id}: {request.reason}")
        
        return {
            "status": "stopped",
            "session_id": session_id,
            "stopped_at": session_info["stopped_at"],
            "reason": request.reason,
            "user_id": request.user_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop stream: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop stream: {str(e)}"
        )


@router.post("/stop-stream/{session_id}")
async def stop_stream_v2(
    session_id: str
) -> Dict[str, Any]:
    """Stop the specified streaming session (API Contract v2.0)
    
    This endpoint matches the API Contract specification for E2E testing.
    It delegates to the main stop_stream endpoint with default parameters.
    """
    try:
        # Create a default request for backward compatibility
        default_request = StreamControlRequest(
            reason="API v2.0 stop request",
            user_id="system"
        )
        
        # Delegate to the main stop_stream function
        result = await stop_stream(session_id, default_request)
        
        # Return response matching API Contract format
        return {
            "status": "success",
            "message": "Stream stopped"
        }
    except HTTPException as e:
        # If session not found, still return success (idempotent)
        if e.status_code == status.HTTP_404_NOT_FOUND:
            logger.info(f"Session {session_id} already stopped or not found")
            return {
                "status": "success",
                "message": "Stream already stopped or not found"
            }
        raise


async def _get_qa_manager():
    """延迟导入 QA Manager 以避免循环导入"""
    from ..core.dependencies import get_qa_manager
    return await get_qa_manager()

@router.post("/sessions/{session_id}/questions")
async def submit_question(
    session_id: str,
    request: QuestionSubmitRequest,
    qa_manager = Depends(_get_qa_manager)  # ✅ 声明式依赖注入（延迟导入）
) -> Dict[str, Any]:
    """Submit a question to the specified session - 🔒 带Redis分布式锁的并发安全版本"""
    
    # 🔒 使用Redis分布式锁防止同会话的并发QA处理
    from ..services.lock_factory import Lock
    import hashlib
    import time
    
    # 生成请求去重标识（基于问题内容和会话）
    request_content = f"{session_id}:{request.text.strip()}"
    request_hash = hashlib.md5(request_content.encode()).hexdigest()[:8]
    
    # 请求追踪ID（用于调试）
    trace_id = f"qa_{int(time.time() * 1000)}_{request_hash}"
    
    lock_key = f"qa_processing:{session_id}"  # 按会话加锁，避免全局阻塞
    holder_id = f"api_handler_{trace_id}"
    
    logger.info(f"📝 QA请求开始处理 - trace_id: {trace_id}, session: {session_id}, question: {request.text[:50]}...")
    
    try:
        # 🔒 60秒超时获取锁
        async with Lock(lock_key, holder_id, timeout_seconds=60):
            logger.info(f"🔒 获取QA处理锁成功 - trace_id: {trace_id}")
            
            # 检查会话是否存在
            session_info = await get_session_info(session_id)
            
            # V2: Use playlist manager for QA insertion
            from ..core.dependencies import get_playlist_manager, get_client_state_tracker
            from ..models.playlist_models import QAInsertionRequest, QAInsertionResult as PlaylistResult, QAInsertionStrategy
            
            playlist_manager = get_playlist_manager()
            client_tracker = get_client_state_tracker()
            
            # ✅ QA Manager 通过 FastAPI Depends 注入，无需手动获取
            
            # 生成QA回答
            qa_start_time = time.time()
            qa_response = await qa_manager.handle_qa({
                "text": request.text,
                "session_id": session_id,
                "trace_id": trace_id,  # 添加追踪ID
                "persona": None,  # Can be configured if needed
                "product_info": {
                    "sku_id": request.sku_id,
                    "product_name": request.product_name
                } if request.sku_id or request.product_name else None
            })
            qa_processing_time = (time.time() - qa_start_time) * 1000
            
            logger.info(f"🤖 QA回答生成完成 - trace_id: {trace_id}, 耗时: {qa_processing_time:.1f}ms, 回答: {qa_response['answer'][:100]}...")
            
            # 创建QA插入请求
            qa_request = QAInsertionRequest(
                qa_id=f"qa_{trace_id}",
                question=request.text,
                answer=qa_response["answer"],
                strategy=QAInsertionStrategy.MIN_POSITION,  # 保守策略确保可靠性
                priority=request.priority.value,
                transition_in="让我来回答这个问题",
                transition_out="希望这个回答对您有帮助"
            )
            
            # 🚀 Phase 2.4: 开始性能监控追踪
            from ..monitoring.qa_performance_monitor import get_qa_performance_monitor, QAInsertionResult
            qa_monitor = get_qa_performance_monitor()
            
            # 开始追踪QA性能
            performance_metric = qa_monitor.start_qa_tracking(
                qa_id=qa_request.qa_id,
                question_text=qa_request.question,
                insertion_strategy=qa_request.strategy.value,
                session_id=session_id,
                client_id=trace_id  # 使用trace_id作为客户端标识
            )
            
            # 记录QA生成时间
            qa_monitor.record_backend_processing_start(qa_request.qa_id)
            
            # 🎯 原子性执行QA插入（在锁保护下）
            insertion_start_time = time.time()
            qa_result = await playlist_manager.handle_qa_event(qa_request, client_tracker)
            insertion_time = (time.time() - insertion_start_time) * 1000
            
            # 记录插入时间到监控器
            qa_monitor.record_playlist_insertion_timing(qa_request.qa_id, insertion_time)
            qa_monitor.record_backend_processing_end(qa_request.qa_id)
            
            queue_size = session_info.get("question_queue_size", 0)
            
            # 完成性能追踪
            result = QAInsertionResult.SUCCESS if qa_result.success else QAInsertionResult.FAILED
            qa_sentences_count = len(qa_result.items_inserted) if qa_result.success else 0
            
            qa_monitor.finish_qa_tracking(
                qa_request.qa_id,
                result=result,
                sentences_inserted=qa_sentences_count,
                error_message=None if qa_result.success else (qa_result.error_message or "QA事件处理失败")
            )
            
            if qa_result.success:
                logger.info(f"✅ QA插入成功 - trace_id: {trace_id}, 插入耗时: {insertion_time:.1f}ms, 句子数: {qa_sentences_count}")
                
                # 🔥 触发 QA 内容的高优先级预合成
                from ..core.dependencies import get_proactive_synthesizer
                proactive_synthesizer = get_proactive_synthesizer()
                if proactive_synthesizer:
                    # 使用返回的同一批items，确保缓存键一致
                    await proactive_synthesizer.trigger_qa_synthesis(qa_result.items_for_pre_synthesis)
                    logger.info(f"✅ 触发 QA 高优先级预合成: {len(qa_result.items_for_pre_synthesis)} 个项目")
                
                # 注意：广播已由 playlist_manager.handle_qa_event 内部处理
                # 不需要额外的广播，避免双重通知导致客户端状态混乱
            else:
                logger.error(f"❌ QA插入失败 - trace_id: {trace_id}")
                # 改进：抛出明确的HTTP异常而不是返回success=false
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={
                        "error": "QA_INSERTION_FAILED",
                        "message": "无法将问答插入播放列表，请稍后重试",
                        "trace_id": trace_id,
                        "session_id": session_id
                    }
                )
            
            total_processing_time = (time.time() - (qa_start_time - (qa_processing_time / 1000))) * 1000
            
            # 只有成功时才返回正常响应
            return {
                "status": "submitted",
                "session_id": session_id,
                "trace_id": trace_id,
                "queue_position": queue_size,
                "priority": request.priority,
                "submitted_at": datetime.utcnow().isoformat(),
                "estimated_wait_seconds": queue_size * 30,
                "processing_metrics": {
                    "qa_generation_ms": round(qa_processing_time, 1),
                    "insertion_ms": round(insertion_time, 1),
                    "total_processing_ms": round(total_processing_time, 1)
                },
                "success": True
            }
            
    except RuntimeError as lock_error:
        # 锁获取失败
        logger.warning(f"⏰ QA处理锁被占用 - trace_id: {trace_id}, error: {lock_error}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"系统正在处理其他QA请求，请稍后再试 (trace_id: {trace_id})"
        )
    except Exception as e:
        logger.error(f"❌ QA处理异常 - trace_id: {trace_id}, error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit question: {str(e)} (trace_id: {trace_id})"
        )


@router.get("/sessions/{session_id}/status")
async def get_session_status(
    session_id: str
) -> Dict[str, Any]:
    """Get detailed status of a streaming session
    
    This response is bound by API_CONTRACT.md v2.4, Section 1.1
    See: design_docs/API_CONTRACT.md#get-session-status
    """
    try:
        session_info = await get_session_info(session_id)
        
        # V2: Get status from playlist manager
        from ..core.dependencies import get_playlist_manager
        playlist_manager = get_playlist_manager()
        playlist_info = await playlist_manager.get_playlist_info()
        
        # Calculate duration if available
        duration_seconds = 0
        start_time_str = session_info.get("started_at", datetime.utcnow().isoformat())
        if session_info.get("started_at"):
            start_time = datetime.fromisoformat(session_info["started_at"].replace('Z', '+00:00'))
            duration_seconds = int((datetime.utcnow().replace(tzinfo=start_time.tzinfo) - start_time).total_seconds())
        
        # Determine status according to contract: active, paused, stopped, error
        is_running = session_info.get("is_running", False)
        is_paused = session_info.get("is_paused", False)
        
        if is_running and not is_paused:
            status_value = "active"
        elif is_paused:
            status_value = "paused"
        else:
            status_value = "stopped"
        
        return {
            "session_id": session_id,  # Contract field
            "status": status_value,  # Contract field: active, paused, stopped, error
            "current_index": 0,  # Contract field: V2 doesn't track segments in the same way
            "total_segments": playlist_info.total_items,  # Contract field
            "connected_clients": 0,  # Contract field: placeholder for V2
            "start_time": start_time_str,  # Contract field
            "duration_seconds": duration_seconds  # Contract field
        }
        
    except Exception as e:
        logger.error(f"Failed to get session status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session status: {str(e)}"
        )


@router.get("/sessions")
async def list_sessions() -> Dict[str, Any]:
    """List all active streaming sessions"""
    try:
        sessions = []
        for session_id, session_data in _active_sessions.items():
            try:
                sessions.append({
                    "session_id": session_id,
                    "is_running": session_data.get("is_running", False),
                    "is_paused": session_data.get("is_paused", False),
                    "mode": session_data.get("mode", "unknown"),
                    "started_at": session_data.get("started_at")
                })
            except Exception as e:
                logger.warning(f"Failed to get status for session {session_id}: {str(e)}")
                
        return {
            "sessions": sessions,
            "total_count": len(sessions),
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to list sessions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list sessions: {str(e)}"
        )


@router.post("/sessions/{session_id}/wait-completion")
async def wait_for_completion(
    session_id: str,
    timeout_seconds: Optional[float] = 300.0
) -> Dict[str, Any]:
    """Wait for a streaming session to complete
    
    Useful for testing and monitoring stream completion.
    """
    try:
        session_info = await get_session_info(session_id)
        import asyncio
        
        start_time = datetime.utcnow()
        
        # V2: Wait for session to complete by checking status
        completed = False
        while (datetime.utcnow() - start_time).total_seconds() < timeout_seconds:
            if not session_info.get("is_running", False):
                completed = True
                break
            await asyncio.sleep(1)
        
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        return {
            "session_id": session_id,
            "completed": completed,
            "wait_duration_seconds": duration,
            "timeout_seconds": timeout_seconds,
            "completed_at": end_time.isoformat() if completed else None
        }
        
    except Exception as e:
        logger.error(f"Failed to wait for completion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to wait for completion: {str(e)}"
        )


# V1 Legacy endpoint - PERMANENTLY DISABLED
@router.post("/start")
async def start_stream_legacy(request: Dict[str, Any] = {}) -> Dict[str, Any]:
    """[DISABLED] V1 legacy start endpoint
    
    This endpoint has been permanently disabled to prevent V1/V2 mixed mode execution.
    The V1 app_config system causes asyncio.run() errors and silent fallback to V1 player.
    """
    logger.error("🚫 Client attempted to use disabled V1 /start endpoint - request rejected")
    logger.error("❌ V1 endpoints permanently disabled due to architecture conflicts")
    logger.error("📍 Use /api/control/start-stream instead")
    
    # Return 410 Gone error to force frontend update
    raise HTTPException(
        status_code=410,  # Gone - permanently unavailable
        detail={
            "error": "V1_ENDPOINT_DISABLED",
            "message": "The /start endpoint has been permanently disabled",
            "new_endpoint": "/api/control/start-stream",
            "reason": "V1 app_config causes asyncio.run() errors and fallback to V1 player",
            "migration_guide": "Update frontend to use /api/control/start-stream"
        }
    )


@router.get("/status")
async def get_default_status() -> Dict[str, Any]:
    """Get status of default session for compatibility
    
    This response is bound by API_CONTRACT.md v2.4, Section 1.1
    See: design_docs/API_CONTRACT.md#get-session-status
    
    Returns status in format defined by API contract.
    """
    try:
        session_info = _active_sessions.get("main")
        
        if not session_info:
            return {
                "session_id": "main",  # Contract field
                "status": "stopped",  # Contract field
                "current_index": 0,  # Contract field
                "total_segments": 0,  # Contract field
                "connected_clients": 0,  # Contract field
                "start_time": datetime.utcnow().isoformat(),  # Contract field
                "duration_seconds": 0  # Contract field
            }
        
        # V2: Get status from playlist manager
        from ..core.dependencies import get_playlist_manager
        playlist_manager = get_playlist_manager()
        playlist_info = await playlist_manager.get_playlist_info()
        
        # Calculate duration if available
        duration_seconds = 0
        if session_info.get("started_at"):
            start_time = datetime.fromisoformat(session_info["started_at"].replace('Z', '+00:00'))
            duration_seconds = int((datetime.utcnow().replace(tzinfo=start_time.tzinfo) - start_time).total_seconds())
        
        # Format according to API contract
        return {
            "session_id": "main",  # Contract field
            "status": "active" if session_info.get("is_running", False) else "paused",  # Contract field: active, paused, stopped, error
            "current_index": 0,  # Contract field: 0-based index, V2 doesn't track segments in the same way
            "total_segments": playlist_info.total_items,  # Contract field
            "connected_clients": 0,  # Contract field: placeholder for V2
            "start_time": session_info.get("started_at", datetime.utcnow().isoformat()),  # Contract field
            "duration_seconds": duration_seconds  # Contract field
        }
        
    except Exception as e:
        logger.error(f"Failed to get status: {str(e)}")
        return {
            "session_id": "main",
            "status": "error",  # Contract field: active, paused, stopped, error
            "current_index": 0,  # Contract field
            "total_segments": 0,  # Contract field
            "connected_clients": 0,  # Contract field
            "start_time": datetime.utcnow().isoformat(),  # Contract field
            "duration_seconds": 0  # Contract field
        }


@router.get("/question-queue")
async def get_question_queue() -> Dict[str, Any]:
    """Get question queue for default session
    
    Returns question queue in format expected by frontend.
    """
    try:
        session_info = _active_sessions.get("main")
        
        if not session_info:
            return {
                "questions": [],
                "total": 0,
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # V2: QA is handled through playlist manager
        # For now, return mock data structure expected by frontend
        return {
            "questions": [],  # V2 doesn't maintain a traditional queue
            "total": 0,
            "processing": False,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get question queue: {str(e)}")
        return {
            "questions": [],
            "total": 0,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# Health check endpoint
@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Check health of control API and active sessions"""
    try:
        healthy_sessions = 0
        unhealthy_sessions = 0
        
        # V2: Check session health
        for session_id, session_data in _active_sessions.items():
            if session_data.get("is_running", False):
                healthy_sessions += 1
            else:
                unhealthy_sessions += 1
        
        return {
            "status": "healthy" if unhealthy_sessions == 0 else "degraded",
            "active_sessions": len(_active_sessions),
            "healthy_sessions": healthy_sessions,
            "unhealthy_sessions": unhealthy_sessions,
            "checked_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "checked_at": datetime.utcnow().isoformat()
        }