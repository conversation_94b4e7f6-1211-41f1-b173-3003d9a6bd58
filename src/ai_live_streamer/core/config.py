"""Configuration management for AI Live Streamer system

Follows Twelve-Factor App principles - all configuration through environment variables.
Raises ConfigError for missing required values (fail-fast principle).
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from loguru import logger

from .exceptions import ConfigError


class Config:
    """Configuration manager with environment variable validation"""
    
    def __init__(self, env_file: Optional[str] = None, config_file: Optional[str] = None) -> None:
        """Initialize configuration from environment variables and YAML config
        
        Args:
            env_file: Optional path to .env file
            config_file: Optional path to config.yml file
            
        Raises:
            ConfigError: If required environment variables are missing
        """
        # Load environment variables
        if env_file:
            load_dotenv(env_file)
        else:
            # Try to load .env from project root
            project_root = Path(__file__).parent.parent.parent.parent
            env_path = project_root / ".env"
            if env_path.exists():
                load_dotenv(env_path)
        
        # Load YAML configuration
        self._yaml_config = {}
        if config_file:
            config_path = Path(config_file)
        else:
            # Try to load config.yml from project root
            project_root = Path(__file__).parent.parent.parent.parent
            config_path = project_root / "config.yml"
            
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._yaml_config = yaml.safe_load(f) or {}
                logger.info(f"YAML configuration loaded from {config_path}")
            except Exception as e:
                logger.warning(f"Failed to load YAML config from {config_path}: {e}")
                self._yaml_config = {}
        
        self._validate_required_vars()
        
        # Log configuration summary for debugging
        self._log_configuration_summary()
        logger.info("Configuration loaded successfully")
    
    def _validate_required_vars(self) -> None:
        """Validate all required environment variables are set"""
        required_vars = [
            "APP_ENV",
            "ELASTICSEARCH_URL", 
            "DEFAULT_TIMEZONE",
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ConfigError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                f"Please check your .env file or environment setup."
            )
    
    def _log_configuration_summary(self) -> None:
        """Log configuration summary showing values and their sources"""
        logger.info("=" * 60)
        logger.info("Configuration Summary:")
        
        # TTS Engine
        env_tts = os.getenv("TTS_ENGINE")
        yaml_tts = self.get_yaml_config('tts.default_engine')
        effective_tts = self.tts_engine
        source = "ENV" if env_tts else ("YAML" if yaml_tts else "DEFAULT")
        logger.info(f"  TTS Engine: '{effective_tts}' (source: {source})")
        
        # LLM Provider
        env_llm = os.getenv("AI_MODEL_PROVIDER")
        yaml_llm = self.get_yaml_config('llm.default_provider')
        effective_llm = self.ai_model_provider
        source = "ENV" if env_llm else ("YAML" if yaml_llm else "DEFAULT")
        logger.info(f"  LLM Provider: '{effective_llm}' (source: {source})")
        
        # App Environment
        logger.info(f"  App Environment: '{self.app_env}' (source: ENV)")
        logger.info(f"  Debug Mode: {self.debug}")
        logger.info("=" * 60)
    
    # === Core Configuration ===
    
    @property
    def app_env(self) -> str:
        return os.getenv("APP_ENV", "development")
    
    @property
    def debug(self) -> bool:
        return os.getenv("DEBUG", "false").lower() == "true"
    
    @property
    def log_level(self) -> str:
        return os.getenv("LOG_LEVEL", "INFO")
    
    # === Database Configuration ===
    
    @property
    def elasticsearch_url(self) -> str:
        url = os.getenv("ELASTICSEARCH_URL")
        if not url:
            raise ConfigError("ELASTICSEARCH_URL is required")
        return url
    
    @property
    def elasticsearch_username(self) -> Optional[str]:
        return os.getenv("ELASTICSEARCH_USERNAME")
    
    @property
    def elasticsearch_password(self) -> Optional[str]:
        return os.getenv("ELASTICSEARCH_PASSWORD")
    
    @property
    def redis_url(self) -> str:
        url = os.getenv("REDIS_URL")
        if not url:
            raise ConfigError("REDIS_URL is required")
        return url
    
    @property
    def database_url(self) -> str:
        """SQLite database URL for operational forms persistence"""
        return os.getenv("DATABASE_URL", "sqlite:///data/operational_forms.db")
    
    @property
    def database_path(self) -> str:
        """Extract file path from SQLite database URL"""
        db_url = self.database_url
        if db_url.startswith("sqlite:///"):
            return db_url[10:]  # Remove "sqlite:///" prefix
        elif db_url.startswith("sqlite://"):
            return db_url[9:]   # Remove "sqlite://" prefix
        else:
            raise ConfigError(f"Invalid SQLite database URL format: {db_url}")
    
    # === LLM Configuration ===
    
    @property
    def openai_api_key(self) -> Optional[str]:
        return os.getenv("OPENAI_API_KEY")
    
    @property
    def anthropic_api_key(self) -> Optional[str]:
        return os.getenv("ANTHROPIC_API_KEY")
    
    @property
    def litellm_master_key(self) -> Optional[str]:
        return os.getenv("LITELLM_MASTER_KEY")
    
    @property
    def litellm_api_base(self) -> str:
        return os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    
    @property
    def litellm_default_model(self) -> str:
        return os.getenv("LITELLM_DEFAULT_MODEL", "gpt-4o-mini")
    
    @property
    def enable_litellm_fallback(self) -> bool:
        return os.getenv("ENABLE_LITELLM_FALLBACK", "true").lower() == "true"
    
    @property
    def ai_model_provider(self) -> str:
        """Get AI model provider with clear priority: ENV > YAML > default"""
        # 1. Check environment variable
        env_var = os.getenv("AI_MODEL_PROVIDER")
        if env_var:
            return env_var
        
        # 2. Check YAML config
        yaml_var = self.get_yaml_config('llm.default_provider')
        if yaml_var:
            return yaml_var
        
        # 3. Fallback to default
        return "qwen-plus"
    
    @property
    def dashscope_api_key(self) -> Optional[str]:
        return os.getenv("DASHSCOPE_API_KEY")
    
    @property
    def qwen_model(self) -> str:
        return os.getenv("QWEN_MODEL", "qwen-plus-2025-04-28")
    
    @property
    def qwen_api_base(self) -> str:
        return os.getenv("QWEN_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    
    # === TTS Configuration ===
    
    @property
    def tts_engine(self) -> str:
        """Get TTS engine with clear priority: ENV > YAML > default
        
        Returns:
            TTS engine name with source tracking for debugging
        """
        # 1. Check environment variable (highest priority)
        env_var = os.getenv("TTS_ENGINE")
        if env_var:
            logger.debug(f"TTS engine from ENV: {env_var}")
            return env_var
        
        # 2. Check YAML config (medium priority)
        yaml_var = self.get_yaml_config('tts.default_engine')
        if yaml_var:
            logger.debug(f"TTS engine from YAML: {yaml_var}")
            return yaml_var
        
        # 3. Fallback to safe default (lowest priority)
        default = "tts_cache_proxy"  # 使用实际存在的引擎
        logger.warning(f"TTS_ENGINE not configured, using default: {default}")
        return default
    
    @property
    def tts_voice(self) -> str:
        return os.getenv("TTS_VOICE", "zh-CN-XiaoxiaoNeural")
    
    @property
    def audio_sample_rate(self) -> int:
        return int(os.getenv("AUDIO_SAMPLE_RATE", "48000"))
    
    @property
    def cozyvoice_model_path(self) -> str:
        return os.getenv("COZYVOICE_MODEL_PATH", "pretrained_models/CosyVoice-300M-SFT")
    
    @property
    def cozyvoice_stream_enabled(self) -> bool:
        return os.getenv("COZYVOICE_STREAM_ENABLED", "true").lower() == "true"
    
    @property
    def cozyvoice_latency_ms(self) -> int:
        return int(os.getenv("COZYVOICE_LATENCY_MS", "150"))
    
    @property
    def cozyvoice_batch_size(self) -> int:
        return int(os.getenv("COZYVOICE_BATCH_SIZE", "4"))
    
    # === Knowledge Base Configuration ===
    
    @property
    def knowledge_base_enabled(self) -> bool:
        """Whether knowledge base search is enabled"""
        return self.get_yaml_config('knowledge_base.enabled', False)
    
    # === Live Streaming Configuration ===
    
    @property
    def default_timezone(self) -> str:
        tz = os.getenv("DEFAULT_TIMEZONE")
        if not tz:
            raise ConfigError("DEFAULT_TIMEZONE is required")
        return tz
    
    @property
    def viewer_count_api_url(self) -> str:
        return os.getenv("VIEWER_COUNT_API_URL", "http://localhost:8000/api/viewers")
    
    @property
    def stream_duration_minutes(self) -> int:
        return int(os.getenv("STREAM_DURATION_MINUTES", "90"))
    
    # === QA Management Configuration ===
    
    @property
    def qa_insertion_offset(self) -> int:
        """QA插入偏移量配置
        
        控制QA内容插入的安全距离:
        - 1: 插入到 current_playing_index + 1 (激进模式，可能需要客户端丢弃缓冲)
        - 2: 插入到 current_playing_index + 2 (稳定模式，确保安全距离)
        
        Returns:
            插入偏移量，默认为2确保最大稳定性
        """
        return int(os.getenv("QA_INSERTION_OFFSET", "2"))
    
    # === Performance Tuning ===
    
    @property
    def max_retrieval_latency_ms(self) -> int:
        return int(os.getenv("MAX_RETRIEVAL_LATENCY_MS", "350"))
    
    @property
    def max_rerank_latency_ms(self) -> int:
        return int(os.getenv("MAX_RERANK_LATENCY_MS", "300"))
    
    @property
    def max_tts_latency_ms(self) -> int:
        return int(os.getenv("MAX_TTS_LATENCY_MS", "800"))
    
    @property
    def max_first_token_latency_ms(self) -> int:
        return int(os.getenv("MAX_FIRST_TOKEN_LATENCY_MS", "800"))
    
    # === Feature Flags ===
    
    @property
    def enable_rerank(self) -> bool:
        return os.getenv("ENABLE_RERANK", "true").lower() == "true"
    
    @property
    def enable_welcome_bot(self) -> bool:
        return os.getenv("ENABLE_WELCOME_BOT", "true").lower() == "true"
    
    @property
    def enable_proactive_engagement(self) -> bool:
        return os.getenv("ENABLE_PROACTIVE_ENGAGEMENT", "true").lower() == "true"
    
    @property
    def enable_diversity_engine(self) -> bool:
        return os.getenv("ENABLE_DIVERSITY_ENGINE", "true").lower() == "true"
    
    @property
    def enable_fail_fast(self) -> bool:
        """Enable fail-fast mode for development debugging
        
        When enabled, errors will immediately raise exceptions instead of
        using fallback mechanisms. Defaults to True in development, False in production.
        """
        # Default based on environment: development=True, production=False
        default_value = "true" if self.app_env == "development" else "false"
        return os.getenv("ENABLE_FAIL_FAST", default_value).lower() == "true"
    
    def get(self, path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (legacy compatibility)
        
        Args:
            path: Dot-separated path to config value (e.g., 'analytics.cache_ttl')
            default: Default value if path not found
            
        Returns:
            Configuration value or default
        """
        return self.get_yaml_config(path, default)
    
    def get_yaml_config(self, path: str, default: Any = None) -> Any:
        """Get configuration value from YAML config using dot notation
        
        Args:
            path: Dot-separated path to config value (e.g., 'llm.providers.qwen-plus.model')
            default: Default value if path not found
            
        Returns:
            Configuration value or default
        """
        keys = path.split('.')
        value = self._yaml_config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_llm_config(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """Get LLM configuration for specified provider
        
        Args:
            provider: LLM provider name, defaults to configured default
            
        Returns:
            LLM configuration dictionary
        """
        if provider is None:
            provider = self.get_yaml_config('llm.default_provider', self.ai_model_provider)
        
        # Special handling for LiteLLM
        if provider in ['litellm', 'gateway', 'unified']:
            return self.get_litellm_config()
        
        return self.get_yaml_config(f'llm.providers.{provider}', {})
    
    def get_litellm_config(self) -> Dict[str, Any]:
        """Get LiteLLM unified gateway configuration
        
        Returns:
            LiteLLM configuration dictionary
        """
        base_config = {
            'api_base': self.litellm_api_base,
            'master_key': self.litellm_master_key,
            'model': self.litellm_default_model,
            'timeout_seconds': 30,
            'retry_attempts': 3,
            'fallback_enabled': self.enable_litellm_fallback
        }
        
        # Merge with YAML configuration if available
        yaml_config = self.get_yaml_config('llm.providers.litellm', {})
        base_config.update(yaml_config)
        
        return base_config
    
    def get_tts_config(self, engine: Optional[str] = None) -> Dict[str, Any]:
        """Get TTS configuration for specified engine
        
        Args:
            engine: TTS engine name, defaults to configured default
            
        Returns:
            TTS configuration dictionary with environment variable substitution
        """
        if engine is None:
            engine = self.get_yaml_config('tts.default_engine', self.tts_engine)
        
        config = self.get_yaml_config(f'tts.engines.{engine}', {})
        
        # Handle environment variable substitution for specific keys
        engine_key = (engine or '').lower()
        if engine_key in ('cosyvoice_v2', 'cosyvoice_unified', 'unified', 'default', 'cozyvoice_cloud', 'cozyvoice') and config:
            # Handle api_endpoint with environment variable fallback
            api_endpoint = config.get('api_endpoint', '')
            if api_endpoint.startswith('${') and api_endpoint.endswith('}'):
                # Parse ${VAR:default} format
                var_spec = api_endpoint[2:-1]  # Remove ${ and }
                if ':' in var_spec:
                    var_name, default_value = var_spec.split(':', 1)
                    config['api_endpoint'] = os.getenv(var_name, default_value)
                else:
                    config['api_endpoint'] = os.getenv(var_spec, '')
            
            # Handle api_key
            api_key = config.get('api_key', '')
            if isinstance(api_key, str) and api_key.startswith('${') and api_key.endswith('}'):
                # Support ${VAR} and ${VAR:default}
                var_spec = api_key[2:-1]
                if ':' in var_spec:
                    var_name, default_value = var_spec.split(':', 1)
                    config['api_key'] = os.getenv(var_name, default_value)
                else:
                    config['api_key'] = os.getenv(var_spec, '')
            
            # Normalize placeholder-like values (e.g., '***' or '${...}') to empty
            if isinstance(config.get('api_key'), str):
                ak = config['api_key'].strip()
                if ak == '***' or (ak.startswith('${') and ak.endswith('}')):
                    config['api_key'] = ''
        
        return config
    
    # === TTS Responsive Playback Configuration ===
    
    @property
    def tts_responsive_playback(self) -> Any:
        """Get TTS responsive playback configuration
        
        Returns configuration object with dot notation access for:
        - audio_gap_threshold: Audio silence gap threshold in seconds
        - min_sentence_duration: Minimum sentence playback duration in seconds  
        - connection_health_check: Enable connection health monitoring
        - debug_logging: Enable detailed debug logging
        - prefetch_sync_check: Enable prefetch queue synchronization check
        """
        class ResponsiveConfig:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    setattr(self, key, value)
        
        config_dict = self.get_yaml_config('tts_responsive_playback', {
            'audio_gap_threshold': 0.8,
            'min_sentence_duration': 0.5,
            'connection_health_check': True,
            'debug_logging': True,
            'prefetch_sync_check': True
        })
        
        return ResponsiveConfig(config_dict)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key with fallback to environment variables
        
        Priority order:
        1. Environment variable (uppercase key)
        2. YAML config (if key contains dots)
        3. Default value
        """
        # First check environment variable
        env_value = os.getenv(key.upper())
        if env_value is not None:
            return env_value
        
        # Then check YAML config if key contains dots
        if '.' in key:
            return self.get_yaml_config(key, default)
        
        # Check if it's a direct YAML key
        yaml_value = self.get_yaml_config(f'lock_manager.{key.lower()}', None)
        if yaml_value is not None:
            return yaml_value
        
        return default
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for debugging"""
        return {
            "app_env": self.app_env,
            "debug": self.debug,
            "log_level": self.log_level,
            "elasticsearch_url": self.elasticsearch_url,
            "redis_url": self.redis_url,
            "tts_engine": self.tts_engine,
            "ai_model_provider": self.ai_model_provider,
            "default_timezone": self.default_timezone,
            "stream_duration_minutes": self.stream_duration_minutes,
            "enable_rerank": self.enable_rerank,
            "enable_welcome_bot": self.enable_welcome_bot,
            "enable_proactive_engagement": self.enable_proactive_engagement,
            "enable_diversity_engine": self.enable_diversity_engine,
            "litellm_api_base": self.litellm_api_base,
            "litellm_default_model": self.litellm_default_model,
            "enable_litellm_fallback": self.enable_litellm_fallback,
            "litellm_master_key": "***" if self.litellm_master_key else None,  # Mask sensitive key
        }


# Global configuration instance
cfg = Config()