#!/usr/bin/env python3
"""
SimpleQAHandler - 极简Q&A处理器 (重构版)

使用统一的新架构引擎，专注于简单高效：
- 无复杂状态管理，立即处理模式
- 使用 CosyVoiceUnifiedEngine 统一TTS管道
- 严格Fail-Fast，错误零容忍
- 基于SDK的流式音频合成和实时广播

Author: Claude Code  
Date: 2025-08-06 (重构)
"""

import asyncio
from typing import Optional, Dict, Any
from datetime import datetime

from loguru import logger

from ..services.tts_engines.cosyvoice_unified_engine import CosyVoiceUnifiedEngine
# V1 imports removed
# from .live_stream_controller import IQAManager


class SimpleQAHandler:
    """极简Q&A处理器 - 无复杂状态，严格Fail-Fast"""
    
    def __init__(self, llm_adapter):
        if not llm_adapter:
            raise ValueError("LLM adapter is required")
            
        self.llm_adapter = llm_adapter
        logger.info("✅ SimpleQAHandler initialized with Fail-Fast principles")
    
    async def handle_qa(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理QA请求 - 实现IQAManager接口
        
        Args:
            question_data: 包含问题数据的字典，支持以下格式：
                - {"question": "问题文本"}
                - {"text": "问题文本"}
                - 其他包含问题文本的格式
                
        Returns:
            Dict[str, Any]: 处理结果
            
        Raises:
            ValueError: 问题数据无效时抛出
            RuntimeError: 处理过程失败时抛出
        """
        if not question_data:
            raise ValueError("Question data cannot be empty")
        
        # 参数适配：从字典中提取问题字符串
        if isinstance(question_data, dict):
            question = question_data.get('question') or question_data.get('text', '')
        else:
            question = str(question_data)
        
        if not question or not question.strip():
            raise ValueError("Question text cannot be empty")
        
        logger.info(f"🤖 Processing QA via interface: {question[:50]}...")
        
        # 调用现有的完整流程方法
        return await self.handle_qa_complete_flow(question.strip())
    
    async def handle_qa_immediately(self, question: str) -> str:
        """立即处理Q&A - 无排队，无状态管理
        
        Args:
            question: 用户问题
            
        Returns:
            str: LLM生成的回答
            
        Raises:
            ValueError: 问题为空时抛出
            RuntimeError: LLM调用失败时抛出
        """
        if not question or not question.strip():
            raise ValueError("Question cannot be empty")
        
        logger.info(f"🤖 Processing Q&A question: {question[:50]}...")
        
        # 直接调用LLM
        try:
            response = await self.llm_adapter.generate_answer(question)
            if not response or not response.strip():
                raise RuntimeError(f"LLM returned empty response for: {question[:50]}")
            
            logger.info(f"🤖 Generated Q&A response: {response[:100]}...")
            return response.strip()
            
        except Exception as e:
            logger.error(f"❌ Failed to generate Q&A response: {e}")
            raise  # Fail-Fast: 直接传播异常
    
    async def play_qa_audio(self, qa_text: str) -> None:
        """播放Q&A音频 - 使用统一的新引擎
        
        Args:
            qa_text: Q&A回答文本
            
        Raises:
            ValueError: 文本为空时抛出
            RuntimeError: TTS播放失败时抛出
        """
        if not qa_text or not qa_text.strip():
            raise ValueError("Q&A text cannot be empty")
        
        logger.info(f"🎤 Starting Q&A audio playback with unified engine: {qa_text[:50]}...")
        
        # 创建独立的统一TTS引擎实例
        qa_tts = None
        try:
            # 使用新的统一引擎和配置
            from ..core.config import cfg
            tts_config = cfg.get_yaml_config('tts_engines.cosyvoice_unified', {})
            qa_tts = CosyVoiceUnifiedEngine(tts_config)
            
            # 初始化引擎
            await qa_tts.initialize()
            
            # 获取音频流代理进行广播
            from ..services.audio_streaming_proxy import get_audio_proxy
            audio_proxy = get_audio_proxy()
            
            # 使用新引擎的流式合成API - 简单直接
            logger.debug(f"🎤 Synthesizing QA audio: {len(qa_text)} characters")
            async for audio_chunk in qa_tts.synthesize_streaming(qa_text):
                if audio_chunk and audio_chunk.audio_stream and audio_chunk.audio_stream.audio_data:
                    # 实时广播QA音频到前端
                    if audio_proxy:
                        await audio_proxy.broadcast_audio_binary(audio_chunk.audio_stream.audio_data)
                    
            logger.info("✅ Q&A audio synthesis and broadcast completed")
            
        except Exception as e:
            logger.error(f"❌ Q&A audio playback failed: {e}")
            raise  # Fail-Fast: 不隐藏错误
        finally:
            # 确保资源清理
            if qa_tts:
                try:
                    await qa_tts.cleanup()
                except Exception as cleanup_error:
                    logger.error(f"❌ Q&A TTS cleanup failed: {cleanup_error}")
                    # 清理错误也要抛出（Fail-Fast）
                    raise cleanup_error
    
    async def handle_qa_complete_flow(self, question: str) -> Dict[str, Any]:
        """处理完整的Q&A流程 - 生成回答并播放音频
        
        Args:
            question: 用户问题
            
        Returns:
            Dict[str, Any]: 处理结果信息
            
        Raises:
            ValueError: 参数无效时抛出
            RuntimeError: 处理过程失败时抛出
        """
        if not question or not question.strip():
            raise ValueError("Question cannot be empty")
        
        start_time = datetime.utcnow()
        
        try:
            # 1. 生成LLM回答
            qa_response = await self.handle_qa_immediately(question)
            
            # 2. 播放Q&A音频
            await self.play_qa_audio(qa_response)
            
            # 3. 返回处理结果
            end_time = datetime.utcnow()
            processing_time = (end_time - start_time).total_seconds()
            
            result = {
                "question": question,
                "answer": qa_response,
                "processing_time_sec": processing_time,
                "success": True,
                "timestamp": end_time.isoformat()
            }
            
            logger.info(f"✅ Q&A complete flow finished in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Q&A complete flow failed: {e}")
            # 返回失败结果，但仍要抛出异常（Fail-Fast）
            raise RuntimeError(f"Q&A processing failed: {e}")


class LLMAdapterMock:
    """LLM适配器模拟类 - 用于测试和开发"""
    
    async def generate_answer(self, question: str) -> str:
        """模拟LLM回答生成"""
        if not question:
            raise ValueError("Question cannot be empty")
        
        # 简单的模拟回答
        mock_answers = {
            "价格": "这个产品的价格非常优惠，现在有特价活动。",
            "质量": "我们保证产品质量，都是经过严格检验的。",
            "配送": "支持全国包邮，24小时内发货。",
            "售后": "提供完善的售后服务，有任何问题随时联系客服。"
        }
        
        # 基于关键词匹配生成回答
        for keyword, answer in mock_answers.items():
            if keyword in question:
                return answer
        
        # 默认回答
        return "感谢您的提问，我们会认真考虑您的建议。如果您还有其他问题，欢迎随时咨询。"


def create_simple_qa_handler(llm_adapter=None) -> SimpleQAHandler:
    """工厂函数：创建简单Q&A处理器
    
    Args:
        llm_adapter: LLM适配器实例，如果为None则使用模拟适配器
        
    Returns:
        SimpleQAHandler: Q&A处理器实例
    """
    if llm_adapter is None:
        logger.warning("⚠️ Using mock LLM adapter for development")
        llm_adapter = LLMAdapterMock()
    
    return SimpleQAHandler(llm_adapter)