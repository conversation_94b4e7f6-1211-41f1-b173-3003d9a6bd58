"""
Script Previewer Service

Generates and previews streaming scripts based on operational form data,
allowing users to review and modify content before going live.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import asyncio
import json
from decimal import Decimal
from loguru import logger

from ..models.forms import (
    OperationalForm, BasicInformation, ProductInformation,
    SellingPointsStructure, SellingPoint, PersonaConfiguration,
    PriorityLevel
)
from ..models.persona import PersonaManager, PersonaConfig
from ..core.exceptions import ServiceError, ValidationError
from ..core.config import cfg
from .llm_script_generator import LLMScriptGenerator, LLMScriptResult


class ScriptSegmentType(Enum):
    """Types of script segments"""
    OPENING = "opening"
    PRODUCT_INTRO = "product_intro"
    SELLING_POINT = "selling_point"
    INTERACTION = "interaction"
    PRICE_ANNOUNCEMENT = "price_announcement"
    CALL_TO_ACTION = "call_to_action"
    TRANSITION = "transition"
    CLOSING = "closing"
    EMERGENCY_FILLER = "emergency_filler"


@dataclass
class ScriptSegment:
    """Individual script segment with metadata"""
    segment_id: str
    segment_type: ScriptSegmentType
    title: str
    content: str
    estimated_duration_seconds: int
    priority: PriorityLevel
    triggers: List[str]  # Conditions that trigger this segment
    variables: Dict[str, Any]  # Dynamic variables in the script
    persona_notes: Optional[str] = None  # Special notes for persona delivery


@dataclass
class ScriptTimeline:
    """Complete script timeline with timing"""
    total_duration_minutes: int
    segments: List[ScriptSegment]
    interaction_points: List[int]  # Time points for viewer interaction
    break_points: List[int]  # Suggested break/pause points
    adaptation_rules: Dict[str, Any]  # Rules for real-time adaptation


@dataclass
class PreviewResult:
    """Result of script preview generation"""
    success: bool
    form_id: str
    script_timeline: Optional[ScriptTimeline]
    preview_html: Optional[str]
    estimated_metrics: Dict[str, Any]
    generation_time_seconds: float
    error_messages: List[str]
    warnings: List[str]


class ScriptPreviewer:
    """
    Advanced script preview generator that creates detailed streaming scripts
    with timing, interaction points, and real-time adaptation capabilities.
    """
    
    def __init__(self):
        self.persona_manager = PersonaManager()
        self.generation_templates = self._load_script_templates()
        self.timing_calculator = TimingCalculator()
        self.llm_generator = LLMScriptGenerator()  # New LLM-based generator
        self.use_llm_generation = True  # Flag to enable LLM generation
        self.preview_stats = {
            "scripts_generated": 0,
            "average_generation_time": 0.0,
            "total_segments_created": 0,
            "preview_requests": 0,
            "llm_generations": 0,
            "template_generations": 0
        }
    
    async def generate_script_preview(self, form: OperationalForm) -> PreviewResult:
        """
        Generate complete script preview with timeline and HTML visualization
        
        Args:
            form: Operational form with all configuration data
            
        Returns:
            PreviewResult with complete script timeline and preview
        """
        start_time = datetime.utcnow()
        result = PreviewResult(
            success=False,
            form_id=form.form_id,
            script_timeline=None,
            preview_html=None,
            estimated_metrics={},
            generation_time_seconds=0.0,
            error_messages=[],
            warnings=[]
        )
        
        try:
            logger.info(f"Generating script preview for form {form.form_id}")
            
            # Step 1: Validate form data for script generation
            validation_result = await self._validate_for_script_generation(form)
            if not validation_result:
                result.error_messages.append("Form data insufficient for script generation")
                return result
            
            # Step 2: Generate script segments using LLM or templates
            if self.use_llm_generation:
                llm_result = await self._generate_llm_script_segments(form)
                if llm_result.success:
                    segments = self._convert_llm_segments_to_script_segments(llm_result.segments)
                    self.preview_stats["llm_generations"] += 1
                else:
                    logger.warning(f"LLM generation failed, falling back to templates: {llm_result.error_messages}")
                    segments = await self._generate_script_segments(form)
                    self.preview_stats["template_generations"] += 1
            else:
                segments = await self._generate_script_segments(form)
                self.preview_stats["template_generations"] += 1
            
            if not segments:
                result.error_messages.append("Failed to generate script segments")
                return result
            
            # Step 3: Calculate timing and create timeline
            timeline = await self._create_script_timeline(form, segments)
            
            # Step 4: Generate HTML preview
            preview_html = await self._generate_html_preview(form, timeline)
            
            # Step 5: Calculate estimated metrics
            metrics = await self._calculate_estimated_metrics(form, timeline)
            
            # Step 6: Validate timeline constraints
            warnings = await self._validate_timeline_constraints(form, timeline)
            
            result.script_timeline = timeline
            result.preview_html = preview_html
            result.estimated_metrics = metrics
            result.warnings = warnings
            result.success = True
            
            # Update stats
            self.preview_stats["scripts_generated"] += 1
            self.preview_stats["total_segments_created"] += len(segments)
            
            logger.info(f"Script preview generated successfully for {form.form_id}")
            
        except Exception as e:
            logger.error(f"Script preview generation failed for {form.form_id}: {str(e)}")
            result.error_messages.append(f"Generation error: {str(e)}")
            
        finally:
            end_time = datetime.utcnow()
            result.generation_time_seconds = (end_time - start_time).total_seconds()
            
            # Update average generation time
            current_avg = self.preview_stats["average_generation_time"]
            scripts_count = self.preview_stats["scripts_generated"]
            if scripts_count > 0:
                new_avg = ((current_avg * (scripts_count - 1)) + result.generation_time_seconds) / scripts_count
                self.preview_stats["average_generation_time"] = new_avg
            
            self.preview_stats["preview_requests"] += 1
        
        return result
    
    async def regenerate_segment(
        self, 
        form: OperationalForm, 
        segment_id: str, 
        modification_notes: Optional[str] = None
    ) -> ScriptSegment:
        """
        Regenerate a specific script segment with modifications
        
        Args:
            form: Operational form data
            segment_id: ID of segment to regenerate
            modification_notes: Optional notes for modifications
            
        Returns:
            Regenerated script segment
        """
        try:
            logger.info(f"Regenerating segment {segment_id} for form {form.form_id}")
            
            # Find segment type based on ID
            segment_type = self._determine_segment_type(segment_id)
            
            # Generate new segment with modifications
            new_segment = await self._generate_single_segment(
                form, segment_type, modification_notes
            )
            
            logger.info(f"Segment {segment_id} regenerated successfully")
            return new_segment
            
        except Exception as e:
            logger.error(f"Segment regeneration failed: {str(e)}")
            raise ServiceError(f"Failed to regenerate segment: {str(e)}")
    
    async def export_script(
        self, 
        form: OperationalForm, 
        timeline: ScriptTimeline,
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """
        Export script in various formats for external use
        
        Args:
            form: Operational form data
            timeline: Script timeline to export
            export_format: Format (json, txt, docx)
            
        Returns:
            Exported script data
        """
        try:
            if export_format == "json":
                return await self._export_as_json(form, timeline)
            elif export_format == "txt":
                return await self._export_as_text(form, timeline)
            elif export_format == "html":
                return await self._export_as_html(form, timeline)
            else:
                raise ValueError(f"Unsupported export format: {export_format}")
                
        except Exception as e:
            logger.error(f"Script export failed: {str(e)}")
            raise ServiceError(f"Export failed: {str(e)}")
    
    async def _validate_for_script_generation(self, form: OperationalForm) -> bool:
        """Validate that form has sufficient data for script generation"""
        required_fields = [
            form.basic_information.stream_title,
            form.product_information.product_name,
            form.selling_points_structure.primary_value_proposition
        ]
        
        return all(field and field.strip() for field in required_fields)
    
    async def _generate_script_segments(self, form: OperationalForm) -> List[ScriptSegment]:
        """Generate all script segments based on form data"""
        segments = []
        
        # Opening segment
        opening = await self._generate_opening_segment(form)
        segments.append(opening)
        
        # Product introduction
        product_intro = await self._generate_product_intro_segment(form)
        segments.append(product_intro)
        
        # Selling point segments
        for i, selling_point in enumerate(form.selling_points_structure.selling_points):
            sp_segment = await self._generate_selling_point_segment(form, selling_point, i)
            segments.append(sp_segment)
        
        # Price announcement
        price_segment = await self._generate_price_segment(form)
        segments.append(price_segment)
        
        # Interaction segments
        interaction_segments = await self._generate_interaction_segments(form)
        segments.extend(interaction_segments)
        
        # Call to action segments
        cta_segments = await self._generate_cta_segments(form)
        segments.extend(cta_segments)
        
        # Closing segment
        closing = await self._generate_closing_segment(form)
        segments.append(closing)
        
        # Emergency filler segments
        filler_segments = await self._generate_filler_segments(form)
        segments.extend(filler_segments)
        
        return segments
    
    async def _generate_opening_segment(self, form: OperationalForm) -> ScriptSegment:
        """Generate opening segment"""
        basic_info = form.basic_information
        product_info = form.product_information
        
        # Use custom greetings if available
        if form.persona_configuration.custom_greetings:
            greeting = form.persona_configuration.custom_greetings[0]
        else:
            greeting = "大家好！欢迎来到我的直播间！"
        
        content = f"""
{greeting}

今天是{datetime.now().strftime('%Y年%m月%d日')}，我是你们的AI主播。

欢迎来到{basic_info.stream_title}！

今天我要为大家介绍一款非常棒的产品 - {product_info.brand}的{product_info.product_name}。

这是一次{self._get_stream_type_description(basic_info.stream_type)}，
我们计划直播{basic_info.planned_duration_minutes}分钟，
为大家详细介绍这款{product_info.category}产品。

请大家在评论区和我互动，有任何问题随时提出！
        """.strip()
        
        return ScriptSegment(
            segment_id="opening_001",
            segment_type=ScriptSegmentType.OPENING,
            title="开场白",
            content=content,
            estimated_duration_seconds=60,
            priority=PriorityLevel.HIGH,
            triggers=["stream_start"],
            variables={
                "greeting": greeting,
                "date": datetime.now().strftime('%Y年%m月%d日'),
                "stream_title": basic_info.stream_title,
                "product_name": product_info.product_name,
                "brand": product_info.brand,
                "duration": basic_info.planned_duration_minutes
            }
        )
    
    async def _generate_product_intro_segment(self, form: OperationalForm) -> ScriptSegment:
        """Generate product introduction segment"""
        product_info = form.product_information
        
        content = f"""
现在让我为大家详细介绍今天的主角 - {product_info.product_name}。

这是{product_info.brand}品牌推出的{product_info.category}产品，
具有以下关键特征：

{self._format_specifications(product_info.key_specifications)}

这款产品的核心优势在于：
{form.selling_points_structure.primary_value_proposition}

接下来我会逐一为大家展示它的各个亮点。
        """.strip()
        
        return ScriptSegment(
            segment_id="product_intro_001",
            segment_type=ScriptSegmentType.PRODUCT_INTRO,
            title="产品介绍",
            content=content,
            estimated_duration_seconds=90,
            priority=PriorityLevel.HIGH,
            triggers=["after_opening"],
            variables={
                "product_name": product_info.product_name,
                "brand": product_info.brand,
                "category": product_info.category,
                "specifications": product_info.key_specifications,
                "value_proposition": form.selling_points_structure.primary_value_proposition
            }
        )
    
    async def _generate_selling_point_segment(
        self, 
        form: OperationalForm, 
        selling_point: SellingPoint, 
        index: int
    ) -> ScriptSegment:
        """Generate segment for a specific selling point"""
        
        content = f"""
现在我们来看第{index + 1}个重要特点：{selling_point.title}

{selling_point.description}

为什么我要特别强调这一点呢？让我用具体的事实来证明：

{self._format_supporting_facts(selling_point.supporting_facts)}

这就是为什么{selling_point.title}对大家来说非常重要的原因。

大家对这个特点有什么想了解的吗？欢迎在评论区提问！
        """.strip()
        
        # Calculate duration based on priority
        duration_map = {
            PriorityLevel.HIGH: 120,
            PriorityLevel.MEDIUM: 90,
            PriorityLevel.LOW: 60
        }
        
        return ScriptSegment(
            segment_id=f"selling_point_{selling_point.point_id}",
            segment_type=ScriptSegmentType.SELLING_POINT,
            title=f"卖点: {selling_point.title}",
            content=content,
            estimated_duration_seconds=duration_map.get(selling_point.priority, 90),
            priority=selling_point.priority,
            triggers=[f"selling_point_{index}"],
            variables={
                "index": index + 1,
                "title": selling_point.title,
                "description": selling_point.description,
                "supporting_facts": selling_point.supporting_facts,
                "priority": selling_point.priority.value
            }
        )
    
    async def _generate_price_segment(self, form: OperationalForm) -> ScriptSegment:
        """Generate price announcement segment"""
        product_info = form.product_information
        
        if product_info.original_price and product_info.original_price > product_info.current_price:
            # Has discount
            discount_percentage = ((product_info.original_price - product_info.current_price) / 
                                   product_info.original_price * 100)
            
            content = f"""
现在我要公布一个重要消息！

{product_info.product_name}的原价是{product_info.original_price}元，
但是今天在我们的直播间，特价只要{product_info.current_price}元！

这相当于为大家节省了{product_info.original_price - product_info.current_price}元，
折扣幅度达到{discount_percentage:.1f}%！

这个优惠价格只在我们直播间有效，过了今天就恢复原价了。
            """.strip()
        else:
            # Regular price
            content = f"""
现在公布{product_info.product_name}的价格：

只需要{product_info.current_price}元！

这个价格对于{product_info.product_name}的品质和功能来说，
性价比是非常高的。

现在下单的朋友还可以享受额外的优惠！
            """.strip()
        
        return ScriptSegment(
            segment_id="price_announcement_001",
            segment_type=ScriptSegmentType.PRICE_ANNOUNCEMENT,
            title="价格公布",
            content=content,
            estimated_duration_seconds=45,
            priority=PriorityLevel.HIGH,
            triggers=["after_selling_points"],
            variables={
                "current_price": float(product_info.current_price),
                "original_price": float(product_info.original_price) if product_info.original_price else None,
                "has_discount": bool(product_info.original_price and product_info.original_price > product_info.current_price)
            }
        )
    
    async def _generate_interaction_segments(self, form: OperationalForm) -> List[ScriptSegment]:
        """Generate interaction segments for viewer engagement"""
        segments = []
        
        interaction_prompts = [
            "大家觉得这个产品怎么样？在评论区告诉我你们的想法！",
            "有没有朋友想了解更多技术细节的？我来为大家详细解答！",
            "看到很多朋友在询问价格，我再次为大家确认一下优惠信息。",
            "评论区的朋友们很活跃啊！我来回答几个大家最关心的问题。",
            "现在直播间的人气很高！新来的朋友欢迎关注我们。"
        ]
        
        for i, prompt in enumerate(interaction_prompts):
            segment = ScriptSegment(
                segment_id=f"interaction_{i+1:03d}",
                segment_type=ScriptSegmentType.INTERACTION,
                title=f"互动环节 {i+1}",
                content=prompt,
                estimated_duration_seconds=30,
                priority=PriorityLevel.MEDIUM,
                triggers=[f"interaction_trigger_{i+1}"],
                variables={"prompt_type": f"engagement_{i+1}"}
            )
            segments.append(segment)
        
        return segments
    
    async def _generate_cta_segments(self, form: OperationalForm) -> List[ScriptSegment]:
        """Generate call-to-action segments"""
        segments = []
        cta_texts = form.selling_points_structure.call_to_actions
        
        for i, cta_text in enumerate(cta_texts):
            content = f"""
{cta_text}

点击下方的购买链接，或者私信我获取购买渠道。

现在下单还有额外优惠，机会难得！
            """.strip()
            
            segment = ScriptSegment(
                segment_id=f"cta_{i+1:03d}",
                segment_type=ScriptSegmentType.CALL_TO_ACTION,
                title=f"行动号召 {i+1}",
                content=content,
                estimated_duration_seconds=25,
                priority=PriorityLevel.HIGH,
                triggers=[f"cta_trigger_{i+1}"],
                variables={"cta_text": cta_text}
            )
            segments.append(segment)
        
        return segments
    
    async def _generate_closing_segment(self, form: OperationalForm) -> ScriptSegment:
        """Generate closing segment"""
        product_info = form.product_information
        
        content = f"""
好的，今天的{form.basic_information.stream_title}就要接近尾声了。

我们详细介绍了{product_info.product_name}的各种优势和特点，
相信大家对这款产品已经有了全面的了解。

如果大家还有任何问题，可以私信我或者在评论区留言，
我会一一为大家解答。

感谢大家今天的陪伴，我们下次直播再见！

记住，{product_info.product_name}，值得拥有！
        """.strip()
        
        return ScriptSegment(
            segment_id="closing_001",
            segment_type=ScriptSegmentType.CLOSING,
            title="结束语",
            content=content,
            estimated_duration_seconds=60,
            priority=PriorityLevel.HIGH,
            triggers=["stream_end"],
            variables={
                "stream_title": form.basic_information.stream_title,
                "product_name": product_info.product_name
            }
        )
    
    async def _generate_filler_segments(self, form: OperationalForm) -> List[ScriptSegment]:
        """Generate emergency filler segments for unexpected situations"""
        segments = []
        
        filler_contents = [
            "让我再次为新加入的朋友介绍一下今天的产品特色。",
            "我看到评论区有朋友在问配送问题，这里为大家统一解答。",
            "趁这个机会，我来回答一下大家最常问的几个问题。",
            "让我为大家展示一下产品的细节部分。",
            "现在我们来看看这款产品与同类产品的对比优势。"
        ]
        
        for i, content in enumerate(filler_contents):
            segment = ScriptSegment(
                segment_id=f"filler_{i+1:03d}",
                segment_type=ScriptSegmentType.EMERGENCY_FILLER,
                title=f"应急内容 {i+1}",
                content=content,
                estimated_duration_seconds=45,
                priority=PriorityLevel.LOW,
                triggers=["emergency_filler"],
                variables={"filler_type": f"emergency_{i+1}"}
            )
            segments.append(segment)
        
        return segments
    
    async def _create_script_timeline(
        self, 
        form: OperationalForm, 
        segments: List[ScriptSegment]
    ) -> ScriptTimeline:
        """Create complete script timeline with timing"""
        
        # Calculate interaction points (every 10-15 minutes)
        target_duration = form.basic_information.planned_duration_minutes
        interaction_interval = 12  # minutes
        interaction_points = list(range(interaction_interval, target_duration, interaction_interval))
        
        # Calculate break points (every 20-30 minutes)
        break_interval = 25  # minutes
        break_points = list(range(break_interval, target_duration, break_interval))
        
        # Define adaptation rules
        adaptation_rules = {
            "low_engagement": {
                "trigger": "interaction_rate < 0.5",
                "action": "insert_interaction_segment"
            },
            "high_engagement": {
                "trigger": "interaction_rate > 2.0", 
                "action": "extend_selling_point_segments"
            },
            "time_running_out": {
                "trigger": "remaining_time < 10",
                "action": "skip_to_cta_segments"
            },
            "ahead_of_schedule": {
                "trigger": "progress > 1.2",
                "action": "insert_filler_segments"
            }
        }
        
        return ScriptTimeline(
            total_duration_minutes=target_duration,
            segments=segments,
            interaction_points=interaction_points,
            break_points=break_points,
            adaptation_rules=adaptation_rules
        )
    
    async def _generate_html_preview(
        self, 
        form: OperationalForm, 
        timeline: ScriptTimeline
    ) -> str:
        """Generate HTML preview of the script timeline"""
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播脚本预览 - {form.basic_information.stream_title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .timeline {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .segment {{
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }}
        .segment.high {{ border-left-color: #dc3545; }}
        .segment.medium {{ border-left-color: #ffc107; }}
        .segment.low {{ border-left-color: #28a745; }}
        .segment-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }}
        .segment-title {{
            font-weight: bold;
            font-size: 18px;
        }}
        .segment-duration {{
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }}
        .segment-content {{
            white-space: pre-line;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
        }}
        .segment-meta {{
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .interaction-point {{
            background: #e8f4f8;
            border: 2px dashed #007bff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            color: #007bff;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>直播脚本预览</h1>
        <h2>{form.basic_information.stream_title}</h2>
        <p><strong>产品:</strong> {form.product_information.brand} {form.product_information.product_name}</p>
        <p><strong>计划时长:</strong> {timeline.total_duration_minutes} 分钟</p>
        <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{len(timeline.segments)}</div>
            <div class="stat-label">脚本片段</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{sum(s.estimated_duration_seconds for s in timeline.segments) // 60}</div>
            <div class="stat-label">预计总时长 (分钟)</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len(timeline.interaction_points)}</div>
            <div class="stat-label">互动节点</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len([s for s in timeline.segments if s.segment_type == ScriptSegmentType.SELLING_POINT])}</div>
            <div class="stat-label">卖点展示</div>
        </div>
    </div>
    
    <div class="timeline">
        <h3>脚本时间线</h3>
        """
        
        current_time = 0
        for i, segment in enumerate(timeline.segments):
            priority_class = segment.priority.value.lower()
            
            # Check if this is near an interaction point
            interaction_marker = ""
            for interaction_time in timeline.interaction_points:
                if abs(current_time / 60 - interaction_time) < 2:
                    interaction_marker = '<div class="interaction-point">🎤 互动时间点</div>'
                    break
            
            html_content += f"""
        {interaction_marker}
        <div class="segment {priority_class}">
            <div class="segment-header">
                <span class="segment-title">{segment.title}</span>
            </div>
            <div class="segment-content">{segment.content}</div>
            <div class="segment-meta">
                类型: {segment.segment_type.value} | 优先级: {segment.priority.value} | 
                触发条件: {', '.join(segment.triggers)}
            </div>
        </div>
            """
            
            current_time += segment.estimated_duration_seconds
        
        html_content += """
    </div>
</body>
</html>
        """
        
        return html_content.strip()
    
    async def _calculate_estimated_metrics(
        self, 
        form: OperationalForm, 
        timeline: ScriptTimeline
    ) -> Dict[str, Any]:
        """Calculate estimated streaming metrics"""
        
        total_words = sum(len(segment.content.replace('\n', ' ').split()) for segment in timeline.segments)
        total_duration_seconds = sum(segment.estimated_duration_seconds for segment in timeline.segments)
        
        words_per_minute = (total_words / total_duration_seconds) * 60 if total_duration_seconds > 0 else 0
        
        selling_point_segments = [s for s in timeline.segments if s.segment_type == ScriptSegmentType.SELLING_POINT]
        selling_point_duration = sum(s.estimated_duration_seconds for s in selling_point_segments)
        
        interaction_segments = [s for s in timeline.segments if s.segment_type == ScriptSegmentType.INTERACTION]
        
        return {
            "total_words": total_words,
            "estimated_duration_minutes": total_duration_seconds / 60,
            "words_per_minute": round(words_per_minute, 1),
            "selling_point_coverage_percentage": round((selling_point_duration / total_duration_seconds) * 100, 1),
            "interaction_opportunities": len(interaction_segments) + len(timeline.interaction_points),
            "content_density": round(total_words / (total_duration_seconds / 60), 1),  # words per minute
            "segment_distribution": {
                segment_type.value: len([s for s in timeline.segments if s.segment_type == segment_type])
                for segment_type in ScriptSegmentType
            }
        }
    
    async def _validate_timeline_constraints(
        self, 
        form: OperationalForm, 
        timeline: ScriptTimeline
    ) -> List[str]:
        """Validate timeline against constraints and return warnings"""
        warnings = []
        
        total_duration_seconds = sum(s.estimated_duration_seconds for s in timeline.segments)
        target_duration_seconds = form.basic_information.planned_duration_minutes * 60
        
        # Check duration alignment
        if total_duration_seconds < target_duration_seconds * 0.8:
            warnings.append(f"脚本内容可能不足，预计时长 {total_duration_seconds/60:.1f} 分钟，目标 {target_duration_seconds/60} 分钟")
        elif total_duration_seconds > target_duration_seconds * 1.2:
            warnings.append(f"脚本内容可能过多，预计时长 {total_duration_seconds/60:.1f} 分钟，目标 {target_duration_seconds/60} 分钟")
        
        # Check selling point coverage
        selling_point_segments = [s for s in timeline.segments if s.segment_type == ScriptSegmentType.SELLING_POINT]
        if len(selling_point_segments) < 3:
            warnings.append("卖点展示片段较少，建议增加产品介绍内容")
        
        # Check interaction frequency
        if len(timeline.interaction_points) < 2:
            warnings.append("互动节点较少，建议增加观众互动机会")
        
        return warnings
    
    def _format_specifications(self, specifications: List[str]) -> str:
        """Format product specifications for script"""
        return "\n".join(f"• {spec}" for spec in specifications)
    
    def _format_supporting_facts(self, facts: List[str]) -> str:
        """Format supporting facts for script"""
        return "\n".join(f"✓ {fact}" for fact in facts)
    
    def _get_stream_type_description(self, stream_type: str) -> str:
        """Get description for stream type"""
        descriptions = {
            "product_launch": "新品发布直播",
            "daily_stream": "日常产品介绍",
            "flash_sale": "限时特卖直播",
            "special_event": "特殊活动直播",
            "q_and_a": "问答互动直播"
        }
        return descriptions.get(stream_type, "产品直播")
    
    def _determine_segment_type(self, segment_id: str) -> ScriptSegmentType:
        """Determine segment type from ID"""
        if segment_id.startswith("opening"):
            return ScriptSegmentType.OPENING
        elif segment_id.startswith("product_intro"):
            return ScriptSegmentType.PRODUCT_INTRO
        elif segment_id.startswith("selling_point"):
            return ScriptSegmentType.SELLING_POINT
        elif segment_id.startswith("interaction"):
            return ScriptSegmentType.INTERACTION
        elif segment_id.startswith("price"):
            return ScriptSegmentType.PRICE_ANNOUNCEMENT
        elif segment_id.startswith("cta"):
            return ScriptSegmentType.CALL_TO_ACTION
        elif segment_id.startswith("closing"):
            return ScriptSegmentType.CLOSING
        else:
            return ScriptSegmentType.EMERGENCY_FILLER
    
    async def _generate_single_segment(
        self, 
        form: OperationalForm, 
        segment_type: ScriptSegmentType, 
        modification_notes: Optional[str] = None
    ) -> ScriptSegment:
        """Generate a single segment of specified type"""
        if segment_type == ScriptSegmentType.OPENING:
            return await self._generate_opening_segment(form)
        elif segment_type == ScriptSegmentType.PRODUCT_INTRO:
            return await self._generate_product_intro_segment(form)
        elif segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
            return await self._generate_price_segment(form)
        elif segment_type == ScriptSegmentType.CLOSING:
            return await self._generate_closing_segment(form)
        else:
            # Default segment
            return ScriptSegment(
                segment_id="generated_segment",
                segment_type=segment_type,
                title="生成的片段",
                content="这是一个动态生成的脚本片段。",
                estimated_duration_seconds=30,
                priority=PriorityLevel.MEDIUM,
                triggers=["dynamic"],
                variables={}
            )
    
    async def _export_as_json(self, form: OperationalForm, timeline: ScriptTimeline) -> Dict[str, Any]:
        """Export script as JSON"""
        return {
            "form_id": form.form_id,
            "export_format": "json",
            "exported_at": datetime.utcnow().isoformat(),
            "timeline": {
                "total_duration_minutes": timeline.total_duration_minutes,
                "segments": [
                    {
                        "segment_id": s.segment_id,
                        "type": s.segment_type.value,
                        "title": s.title,
                        "content": s.content,
                        "duration_seconds": s.estimated_duration_seconds,
                        "priority": s.priority.value,
                        "triggers": s.triggers,
                        "variables": s.variables
                    }
                    for s in timeline.segments
                ],
                "interaction_points": timeline.interaction_points,
                "break_points": timeline.break_points
            }
        }
    
    # === LLM Integration Methods ===
    
    async def _generate_llm_script_segments(self, form: OperationalForm) -> LLMScriptResult:
        """Generate script segments using LLM"""
        try:
            # Try to get persona configuration from form
            persona_config = await self._extract_persona_from_form(form)
            
            # Generate script using LLM
            return await self.llm_generator.generate_script(form, persona_config)
            
        except Exception as e:
            logger.error(f"LLM script generation failed: {e}")
            return LLMScriptResult(
                success=False,
                form_id=form.form_id,
                segments=[],
                total_duration_seconds=0,
                persona_info={},
                generation_stats={},
                error_messages=[f"LLM generation failed: {str(e)}"],
                warnings=[]
            )
    
    def _convert_llm_segments_to_script_segments(self, llm_segments) -> List[ScriptSegment]:
        """Convert LLM segments to legacy ScriptSegment format"""
        converted_segments = []
        
        for llm_segment in llm_segments:
            # Convert LLMScriptSegment.segment_type (enum) to ScriptSegmentType (enum)
            try:
                # Try to map from LLM segment type to preview segment type
                segment_type = ScriptSegmentType(llm_segment.segment_type.value)
            except (ValueError, AttributeError):
                # Fallback to default if conversion fails
                logger.warning(f"Could not convert segment type {llm_segment.segment_type}, using EMERGENCY_FILLER")
                segment_type = ScriptSegmentType.EMERGENCY_FILLER
            
            script_segment = ScriptSegment(
                segment_id=llm_segment.segment_id,
                segment_type=segment_type,
                title=llm_segment.title,
                content=llm_segment.content,
                estimated_duration_seconds=llm_segment.estimated_duration_seconds,
                priority=llm_segment.priority,
                triggers=llm_segment.triggers,
                variables=llm_segment.variables,
                persona_notes=None  # LLMScriptSegment doesn't have persona_notes
            )
            converted_segments.append(script_segment)
        
        return converted_segments
    
    async def _extract_persona_from_form(self, form: OperationalForm) -> Optional[PersonaConfig]:
        """Extract or create persona configuration from form data"""
        try:
            # Check if form has persona configuration
            if hasattr(form, 'persona_configuration') and form.persona_configuration:
                persona_cfg = form.persona_configuration
                
                # Convert form persona to PersonaConfig
                from ..models.persona import PersonaType, VoiceStyle
                
                # Map voice style
                voice_style_map = {
                    "gentle": VoiceStyle.GENTLE,
                    "energetic": VoiceStyle.ENERGETIC,
                    "warm": VoiceStyle.WARM,
                    "professional": VoiceStyle.PROFESSIONAL,
                    "casual": VoiceStyle.CASUAL,
                    "soothing": VoiceStyle.SOOTHING
                }
                
                return PersonaConfig(
                    persona_id=f"form_{form.form_id}_persona",
                    name=persona_cfg.persona_name or "AI主播",
                    description="基于表单配置的个性化主播",
                    persona_type=PersonaType.TEMPLATE_BASED,
                    voice_style=voice_style_map.get(persona_cfg.voice_style, VoiceStyle.WARM),
                    speaking_rate=persona_cfg.speaking_rate or 1.0,
                    consistency_temperature=0.3
                )
            
            # Return default persona if no configuration
            return None
            
        except Exception as e:
            logger.warning(f"Failed to extract persona from form: {e}")
            return None
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """Get current generation statistics"""
        total_generations = self.preview_stats.get("llm_generations", 0) + self.preview_stats.get("template_generations", 0)
        llm_ratio = (self.preview_stats.get("llm_generations", 0) / total_generations * 100) if total_generations > 0 else 0
        
        base_stats = {
            **self.preview_stats,
            "total_generations": total_generations,
            "llm_usage_percentage": round(llm_ratio, 1),
            "llm_enabled": self.use_llm_generation
        }
        
        # Add LLM generator detailed stats if available
        if hasattr(self.llm_generator, 'get_detailed_stats'):
            llm_detailed_stats = self.llm_generator.get_detailed_stats()
            base_stats["llm_detailed"] = llm_detailed_stats
            
        return base_stats
    
    def toggle_llm_generation(self, enabled: bool = None) -> bool:
        """Toggle LLM generation on/off"""
        if enabled is not None:
            self.use_llm_generation = enabled
        else:
            self.use_llm_generation = not self.use_llm_generation
        
        logger.info(f"LLM generation {'enabled' if self.use_llm_generation else 'disabled'}")
        return self.use_llm_generation
    
    # === Advanced LLM Integration Methods ===
    
    async def generate_multiple_script_versions(self, form: OperationalForm, versions: int = 3) -> List[Dict[str, Any]]:
        """Generate multiple versions of scripts for comparison"""
        if not self.use_llm_generation:
            logger.warning("LLM generation is disabled, cannot generate multiple versions")
            return []
        
        try:
            persona_config = await self._extract_persona_from_form(form)
            versions_results = await self.llm_generator.generate_multiple_versions(form, persona_config, versions)
            
            # Convert to preview format
            preview_versions = []
            for i, result in enumerate(versions_results):
                if result.success:
                    segments = self._convert_llm_segments_to_script_segments(result.segments) 
                    timeline = await self._create_script_timeline(form, segments)
                    preview_html = await self._generate_html_preview(form, timeline)
                    
                    preview_versions.append({
                        "version_id": f"v{i+1}",
                        "segments_count": len(segments),
                        "total_duration": result.total_duration_seconds,
                        "generation_stats": result.generation_stats,
                        "preview_html": preview_html,
                        "quality_indicators": self._analyze_version_quality(result)
                    })
            
            logger.info(f"Generated {len(preview_versions)} script versions")
            return preview_versions
            
        except Exception as e:
            logger.error(f"Failed to generate multiple versions: {e}")
            return []
    
    async def regenerate_specific_segment(self, form: OperationalForm, segment_type: str) -> Dict[str, Any]:
        """Regenerate a specific segment and return updated preview"""
        if not self.use_llm_generation:
            return {"success": False, "error": "LLM generation is disabled"}
        
        try:
            # Convert string to enum
            segment_type_enum = ScriptSegmentType(segment_type)
            persona_config = await self._extract_persona_from_form(form)
            
            # Regenerate the segment
            new_segment = await self.llm_generator.regenerate_segment(form, segment_type_enum, persona_config)
            
            if new_segment:
                # Convert to script segment format
                script_segment = self._convert_llm_segments_to_script_segments([new_segment])[0]
                
                return {
                    "success": True,
                    "segment": {
                        "segment_id": script_segment.segment_id,
                        "title": script_segment.title,
                        "content": script_segment.content,
                        "duration_seconds": script_segment.estimated_duration_seconds,
                        "generation_metadata": new_segment.generation_metadata
                    }
                }
            else:
                return {"success": False, "error": "Failed to regenerate segment"}
                
        except Exception as e:
            logger.error(f"Failed to regenerate segment: {e}")
            return {"success": False, "error": str(e)}
    
    def update_llm_generation_config(self, config_updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update LLM generation configuration"""
        try:
            self.llm_generator.update_generation_config(config_updates)
            return {"success": True, "config": self.llm_generator.get_generation_config()}
        except Exception as e:
            logger.error(f"Failed to update LLM config: {e}")
            return {"success": False, "error": str(e)}
    
    def _analyze_version_quality(self, result: 'LLMScriptResult') -> Dict[str, Any]:
        """Analyze quality indicators for a script version"""
        try:
            total_tokens = sum(seg.generation_metadata.get("tokens_used", 0) for seg in result.segments)
            avg_response_time = sum(seg.generation_metadata.get("response_time_ms", 0) for seg in result.segments) / len(result.segments)
            
            # Quality indicators
            indicators = {
                "content_length": sum(len(seg.content) for seg in result.segments),
                "segment_count": len(result.segments),
                "total_tokens_used": total_tokens,
                "average_response_time_ms": round(avg_response_time, 1),
                "duration_accuracy": abs(result.total_duration_seconds - (60 * 60)) / (60 * 60),  # Assuming 60min target
                "persona_consistency": 1.0 if result.persona_info else 0.5
            }
            
            # Overall quality score (0-1)
            quality_factors = [
                min(1.0, indicators["content_length"] / 2000),  # Optimal ~2000 chars
                min(1.0, indicators["segment_count"] / 6),      # Optimal ~6 segments
                max(0.0, 1.0 - indicators["duration_accuracy"]), # Lower deviation = higher score
                indicators["persona_consistency"]
            ]
            
            indicators["overall_quality"] = round(sum(quality_factors) / len(quality_factors), 2)
            
            return indicators
            
        except Exception as e:
            logger.warning(f"Quality analysis failed: {e}")
            return {"overall_quality": 0.5, "error": str(e)}
    
    async def get_generation_insights(self) -> Dict[str, Any]:
        """Get insights about generation performance and trends"""
        try:
            stats = self.get_generation_stats()
            
            insights = {
                "generation_efficiency": {
                    "llm_adoption_rate": stats.get("llm_usage_percentage", 0),
                    "average_generation_time": stats.get("average_generation_time", 0),
                    "total_requests": stats.get("total_generations", 0)
                },
                "quality_metrics": {},
                "recommendations": []
            }
            
            # Add LLM specific insights
            if "llm_detailed" in stats:
                llm_stats = stats["llm_detailed"]
                insights["quality_metrics"] = {
                    "quality_adjustment_rate": llm_stats.get("quality_adjustment_rate", 0),
                    "regeneration_rate": llm_stats.get("regeneration_rate", 0),
                    "average_tokens_per_script": llm_stats.get("average_tokens_per_script", 0)
                }
                
                # Generate recommendations
                if llm_stats.get("quality_adjustment_rate", 0) > 0.3:
                    insights["recommendations"].append("考虑调整生成参数以提高初次生成质量")
                
                if llm_stats.get("regeneration_rate", 0) > 0.2:
                    insights["recommendations"].append("用户经常重新生成内容，可能需要优化提示词模板")
                
                if llm_stats.get("average_tokens_per_script", 0) > 2000:
                    insights["recommendations"].append("生成内容可能过长，建议优化长度控制")
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to generate insights: {e}")
            return {"error": str(e)}
    
    async def _export_as_text(self, form: OperationalForm, timeline: ScriptTimeline) -> Dict[str, Any]:
        """Export script as plain text"""
        text_content = f"""
直播脚本 - {form.basic_information.stream_title}
产品: {form.product_information.brand} {form.product_information.product_name}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

========================================

"""
        
        for segment in timeline.segments:
            text_content += f"""
【{segment.title}】({segment.estimated_duration_seconds}秒)
{segment.content}

----------------------------------------
"""
        
        return {
            "form_id": form.form_id,
            "export_format": "text",
            "exported_at": datetime.utcnow().isoformat(),
            "content": text_content
        }
    
    async def _export_as_html(self, form: OperationalForm, timeline: ScriptTimeline) -> Dict[str, Any]:
        """Export script as standalone HTML"""
        html_content = await self._generate_html_preview(form, timeline)
        
        return {
            "form_id": form.form_id,
            "export_format": "html",
            "exported_at": datetime.utcnow().isoformat(),
            "content": html_content
        }
    
    def _load_script_templates(self) -> Dict[str, Any]:
        """Load script generation templates"""
        return {
            "opening_templates": [
                "欢迎大家来到{stream_title}！",
                "大家好，今天给大家介绍{product_name}！",
                "直播开始了，感谢大家的支持！"
            ],
            "transition_phrases": [
                "接下来我们来看看",
                "现在让我为大家介绍",
                "下面重点展示"
            ],
            "interaction_prompts": [
                "大家有什么问题吗？",
                "评论区很活跃啊！",
                "新来的朋友欢迎关注！"
            ]
        }
    
    def get_preview_stats(self) -> Dict[str, Any]:
        """Get current preview generation statistics"""
        return self.preview_stats.copy()


class TimingCalculator:
    """Helper class for calculating script timing"""
    
    def __init__(self):
        self.words_per_minute_base = 160  # Base speaking rate
        self.pause_factor = 1.2  # Factor for natural pauses
    
    def calculate_segment_duration(self, content: str, speaking_rate: float = 1.0) -> int:
        """Calculate estimated duration for a content segment"""
        word_count = len(content.replace('\n', ' ').split())
        adjusted_wpm = self.words_per_minute_base * speaking_rate / self.pause_factor
        duration_minutes = word_count / adjusted_wpm
        return int(duration_minutes * 60)  # Convert to seconds