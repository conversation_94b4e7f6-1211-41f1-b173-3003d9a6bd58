"""LiteLLM Unified Adapter implementation

Implements LLM interface using LiteLLM as a unified model gateway
for multi-provider model access with automatic load balancing and fallback.
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, AsyncGenerator
from loguru import logger

from .base import BaseLLMAdapter, LLMMessage, LLMResponse
from ...core.exceptions import ServiceError
from ...core.config import cfg


def _create_service_error(message: str, error_code: Optional[str] = None, **kwargs) -> ServiceError:
    """Create ServiceError with litellm service name pre-filled
    
    Args:
        message: Error message
        error_code: Optional error code
        **kwargs: Additional ServiceError parameters
        
    Returns:
        ServiceError instance with service_name="litellm"
    """
    return ServiceError(message, service_name="litellm", error_code=error_code, **kwargs)


class LiteLLMAdapter(BaseLLMAdapter):
    """LiteLLM unified adapter for multi-provider model access"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LiteLLM adapter
        
        Args:
            config: LiteLLM configuration from config.yml
        """
        super().__init__(config)
        self._client = None
        self._model = config.get('model', cfg.ai_model_provider)
        self._api_base = config.get('api_base', 'http://localhost:4000')
        self._timeout = config.get('timeout_seconds', 30)
        self._retry_attempts = config.get('retry_attempts', 3)
        self._master_key = config.get('master_key', cfg.litellm_master_key)
        
        logger.info(f"LiteLLM adapter initialized with model: {self._model}")
    
    async def initialize(self) -> None:
        """Initialize LiteLLM client"""
        try:
            # Import LiteLLM
            import litellm
            from litellm.caching.caching import Cache
            from openai import AsyncOpenAI
            
            # Check if master key is available
            if not self._master_key:
                raise _create_service_error(
                    "LITELLM_MASTER_KEY not found in environment variables"
                )
            
            # Configure LiteLLM settings
            litellm.set_verbose = False  # Reduce noise in logs
            litellm.drop_params = True   # Drop unsupported parameters
            
            # Enable LiteLLM local caching
            cache_enabled = self.config.get('cache_enabled', True)
            if cache_enabled:
                litellm.cache = Cache()
                logger.info("LiteLLM local caching enabled")
            
            # Create OpenAI-compatible client pointing to LiteLLM proxy
            self._client = AsyncOpenAI(
                api_key=self._master_key,
                base_url=self._api_base,
                timeout=self._timeout
            )
            
            # Test the connection
            await self._test_connection()
            
            self.is_initialized = True
            logger.info("LiteLLM client initialized successfully")
            
        except ImportError as e:
            raise _create_service_error(
                "LiteLLM not installed. Install with: pip install litellm"
            ) from e
        except Exception as e:
            logger.error(f"Failed to initialize LiteLLM adapter: {e}")
            raise _create_service_error(f"LiteLLM initialization failed: {e}")
    
    async def _test_connection(self) -> None:
        """Test the connection to LiteLLM proxy"""
        try:
            # Make a simple test request
            test_messages = [
                {"role": "user", "content": "Hello"}
            ]
            
            response = await self._client.chat.completions.create(
                model=self._model,
                messages=test_messages,
                max_tokens=5,
                timeout=5
            )
            
            logger.debug("LiteLLM connection test successful")
            
        except Exception as e:
            logger.error(f"LiteLLM connection test failed: {e}")
            raise _create_service_error(f"Failed to connect to LiteLLM proxy: {e}")
    
    async def generate(
        self, 
        messages: List[LLMMessage], 
        **kwargs
    ) -> LLMResponse:
        """Generate response using LiteLLM
        
        Args:
            messages: Conversation messages
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse with generated content
        """
        if not self.is_initialized:
            await self.initialize()
        
        if not self.validate_messages(messages):
            raise _create_service_error("Invalid message format")
        
        start_time = time.time()
        
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Merge default parameters with kwargs
            params = self.get_default_parameters()
            params.update(kwargs)
            params['stream'] = False  # Non-streaming mode
            
            # Enable caching if requested and supported
            cache_enabled = params.pop('caching', False)
            if cache_enabled:
                params['caching'] = True
            
            # Make request with retry logic
            response = await self._make_request_with_retry(
                openai_messages, params
            )
            
            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000
            
            # Extract response data
            choice = response.choices[0]
            content = choice.message.content
            
            # Create standardized response
            llm_response = LLMResponse(
                content=content,
                model=response.model,
                usage={
                    'prompt_tokens': response.usage.prompt_tokens if response.usage else 0,
                    'completion_tokens': response.usage.completion_tokens if response.usage else 0,
                    'total_tokens': response.usage.total_tokens if response.usage else 0
                },
                finish_reason=choice.finish_reason,
                response_time_ms=response_time_ms
            )
            
            logger.debug(f"LiteLLM generated {len(content)} chars in {response_time_ms:.1f}ms")
            return llm_response
            
        except Exception as e:
            logger.error(f"LiteLLM generation failed: {e}")
            raise _create_service_error(f"Generation failed: {e}")
    
    async def generate_streaming(
        self, 
        messages: List[LLMMessage], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response using LiteLLM
        
        Args:
            messages: Conversation messages
            **kwargs: Additional parameters
            
        Yields:
            Partial response chunks
        """
        if not self.is_initialized:
            await self.initialize()
        
        if not self.validate_messages(messages):
            raise _create_service_error("Invalid message format")
        
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Merge parameters with streaming enabled
            params = self.get_default_parameters()
            params.update(kwargs)
            params['stream'] = True
            
            # Make streaming request
            async for chunk_content in self._make_streaming_request(openai_messages, params):
                if chunk_content:  # Skip empty chunks
                    yield chunk_content
                    
        except Exception as e:
            logger.error(f"LiteLLM streaming generation failed: {e}")
            raise _create_service_error(f"Streaming generation failed: {e}")
    
    async def generate_answer(self, question: str) -> str:
        """Generate an answer for Q&A scenarios
        
        Args:
            question: The user's question
            
        Returns:
            The LLM's answer as a string
            
        Raises:
            ServiceError: If generation fails
        """
        if not question or not question.strip():
            raise _create_service_error("Question cannot be empty")
        
        # Convert question to LLMMessage format
        messages = [LLMMessage(role="user", content=question.strip())]
        
        try:
            # Use the standard generate method
            response = await self.generate(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"LiteLLM generate_answer failed: {e}")
            raise _create_service_error(f"Failed to generate answer: {e}")
    
    async def _make_request_with_retry(
        self, 
        messages: List[Dict[str, str]], 
        params: Dict[str, Any]
    ):
        """Make request with retry logic"""
        last_exception = None
        
        for attempt in range(self._retry_attempts):
            try:
                # Use LiteLLM completion function directly for better caching support
                import litellm
                
                # Prepare parameters for LiteLLM
                litellm_params = {
                    'model': self._model,
                    'messages': messages,
                    **params
                }
                
                # Make async completion call
                response = await litellm.acompletion(**litellm_params)
                return response
                
            except Exception as e:
                last_exception = e
                logger.warning(f"LiteLLM request attempt {attempt + 1} failed: {e}")
                
                if attempt < self._retry_attempts - 1:
                    # Wait before retry (exponential backoff)
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)
                    
        # All retries failed
        raise _create_service_error(f"All {self._retry_attempts} attempts failed: {last_exception}")
    
    async def _make_streaming_request(
        self, 
        messages: List[Dict[str, str]], 
        params: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """Make streaming request"""
        try:
            stream = await self._client.chat.completions.create(
                model=self._model,
                messages=messages,
                **params
            )
            
            async for chunk in stream:
                if chunk.choices:
                    delta = chunk.choices[0].delta
                    if delta.content:
                        yield delta.content
                        
        except Exception as e:
            logger.error(f"Streaming request failed: {e}")
            raise
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict[str, str]]:
        """Convert LLMMessage objects to OpenAI format
        
        Args:
            messages: List of LLMMessage objects
            
        Returns:
            List of message dictionaries
        """
        openai_messages = []
        
        for msg in messages:
            openai_msg = {
                'role': msg.role,
                'content': msg.content
            }
            
            if msg.name:
                openai_msg['name'] = msg.name
                
            openai_messages.append(openai_msg)
        
        return openai_messages
    
    async def cleanup(self) -> None:
        """Clean up LiteLLM resources"""
        if self._client:
            # Close the client if it has a close method
            if hasattr(self._client, 'close'):
                await self._client.close()
            self._client = None
        
        self.is_initialized = False
        logger.info("LiteLLM adapter cleaned up")
    
    @property
    def supports_streaming(self) -> bool:
        """LiteLLM supports streaming"""
        return True
    
    @property
    def supports_caching(self) -> bool:
        """LiteLLM supports caching"""
        return True
    
    @property
    def provider_name(self) -> str:
        """Provider name"""
        return "litellm"
    
    @property
    def model_name(self) -> str:
        """Model name

        Returns the model name as configured, without any provider prefix.
        This allows PydanticAI agents to handle the model name formatting
        appropriately based on their own logic.
        """
        return self._model