# /beautify
You are a top UI designer tasked with polishing an existing web page’s **visual layer only**.
Rewrite *only* the CSS provided below so the page matches a modern, minimalist SaaS aesthetic
while strictly preserving structure, classes and JavaScript behaviour.

---
### Visual spec  (❗️edit these tokens to match your brand)
- Base background (`#f7f8fa`)
- Primary accent (`#2e7ff2`), secondary accent (`#00c49a`)
- Text colors: `#1e1e20` (primary), `#50525a` (secondary)
- Font family: “Inter”, fallback sans-serif
- Type scale: 14 / 20 / 24 px; weights 400 & 600
- Spacing system: 8-px grid; component outer margin = 2 × spacing
- Radius 4 px; card shadow `0 1px 2px rgba(0,0,0,.04)`
- Breakpoints: ≥1280 px 4-column, <1280 px 2-column, <768 px 1-column
- Buttons: 150 ms `ease-out` opacity transition on `:hover,:focus`

### Must NOT
- Change any HTML or class names
- Add or remove JavaScript
- Hard-code new colors outside the palette
- Output anything except the final CSS block

"""  /* Original CSS starts here */
{{selection}}
"""  /* Original CSS ends here */

Output **only** the revised CSS inside a fenced ```css``` block, no explanations.
