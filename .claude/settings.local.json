{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(source:*)", "Bash(python -m pytest tests/test_operational_forms.py -v)", "Bash(python -m pytest tests/test_operational_forms.py::test_selling_point_creation -v --no-cov)", "Bash(python -m pytest tests/test_operational_forms.py -v --override-ini=\"addopts=\")", "Bash(python -m pytest tests/test_validation_fixes.py -v --override-ini=\"addopts=\")", "WebFetch(domain:github.com)", "Bash(python tests/test_litellm_integration.py)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(python -m pytest tests/test_persistence.py -v)", "Bash(python -m pytest tests/test_persistence.py -v --tb=short)", "WebFetch(domain:help.aliyun.com)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(conda info:*)", "WebFetch(domain:ai.pydantic.dev)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(curl:*)", "Bash(uvicorn src.ai_live_streamer.api.app:app --host 127.0.0.1 --port 8000 --reload --no-access-log)", "Bash(git ls-tree:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(pkill:*)", "Bash(node:*)", "<PERSON><PERSON>(timeout 60 python:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./init-claude-pipeline.sh:*)", "mcp__shell-gemini__shell_execute", "Bash(kill:*)", "mcp__gemini-cli__ask-gemini", "Bash(conda activate:*)", "Bash(timeout 30 python -m pytest tests/unit/test_live_stream_controller.py::TestLiveStreamControllerBasics::test_controller_initialization -v --override-ini=\"addopts=\")", "Bash(system_profiler:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(bash:*)", "Bash(git log:*)"], "deny": [], "defaultMode": "bypassPermissions"}}